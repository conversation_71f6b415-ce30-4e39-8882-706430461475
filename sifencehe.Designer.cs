﻿namespace ANYE_Balls
{
    partial class sifencehe
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewsfch = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxsfchtq = new AntdUI.Checkbox();
            textboxsfchjcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxsifencehe = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxsifencehe = new AntdUI.Checkbox();
            buttonsfchcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewsfch).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewsfch
            // 
            datagridviewsfch.AllowUserToAddRows = false;
            datagridviewsfch.AllowUserToDeleteRows = false;
            datagridviewsfch.AllowUserToResizeColumns = false;
            datagridviewsfch.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewsfch.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewsfch.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewsfch.BorderStyle = BorderStyle.Fixed3D;
            datagridviewsfch.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewsfch.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewsfch.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewsfch.ColumnHeadersHeight = 40;
            datagridviewsfch.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewsfch.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewsfch.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewsfch.EnableHeadersVisualStyles = false;
            datagridviewsfch.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewsfch.GridColor = Color.AliceBlue;
            datagridviewsfch.Location = new Point(309, 184);
            datagridviewsfch.Name = "datagridviewsfch";
            datagridviewsfch.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewsfch.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewsfch.RowHeadersVisible = false;
            datagridviewsfch.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewsfch.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewsfch.RowTemplate.Height = 25;
            datagridviewsfch.ScrollBars = ScrollBars.None;
            datagridviewsfch.SelectedIndex = -1;
            datagridviewsfch.Size = new Size(294, 300);
            datagridviewsfch.TabIndex = 58;
            datagridviewsfch.CellValidating += datagridviewsfch_CellValidating;
            datagridviewsfch.CellValueChanged += datagridviewsfch_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxsfchtq
            // 
            checkboxsfchtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsfchtq.Location = new Point(607, 186);
            checkboxsfchtq.Name = "checkboxsfchtq";
            checkboxsfchtq.Size = new Size(131, 23);
            checkboxsfchtq.TabIndex = 56;
            checkboxsfchtq.Text = "自动吐球";
            checkboxsfchtq.CheckedChanged += checkboxsfchtq_CheckedChanged;
            // 
            // textboxsfchjcfd
            // 
            textboxsfchjcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxsfchjcfd.Location = new Point(125, 231);
            textboxsfchjcfd.Margin = new Padding(4, 5, 4, 5);
            textboxsfchjcfd.MinimumSize = new Size(1, 16);
            textboxsfchjcfd.Name = "textboxsfchjcfd";
            textboxsfchjcfd.Padding = new Padding(5);
            textboxsfchjcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxsfchjcfd.ShowText = false;
            textboxsfchjcfd.Size = new Size(70, 30);
            textboxsfchjcfd.TabIndex = 53;
            textboxsfchjcfd.Text = "0";
            textboxsfchjcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxsfchjcfd.Watermark = "";
            textboxsfchjcfd.TextChanged += textboxsfchjcfd_TextChanged;
            textboxsfchjcfd.KeyPress += textboxsfchjcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 52;
            label1.Text = "交叉幅度";
            // 
            // comboboxsifencehe
            // 
            comboboxsifencehe.DataSource = null;
            comboboxsifencehe.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxsifencehe.DropDownWidth = 70;
            comboboxsifencehe.FillColor = Color.White;
            comboboxsifencehe.FillColorGradient = true;
            comboboxsifencehe.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxsifencehe.ItemHeight = 40;
            comboboxsifencehe.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxsifencehe.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxsifencehe.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxsifencehe.Location = new Point(125, 180);
            comboboxsifencehe.Margin = new Padding(0);
            comboboxsifencehe.MaxDropDownItems = 30;
            comboboxsifencehe.MinimumSize = new Size(63, 0);
            comboboxsifencehe.Name = "comboboxsifencehe";
            comboboxsifencehe.Padding = new Padding(0, 0, 30, 10);
            comboboxsifencehe.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxsifencehe.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxsifencehe.ScrollBarStyleInherited = false;
            comboboxsifencehe.Size = new Size(70, 31);
            comboboxsifencehe.SymbolSize = 24;
            comboboxsifencehe.TabIndex = 51;
            comboboxsifencehe.Text = "A";
            comboboxsifencehe.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxsifencehe.Watermark = "";
            comboboxsifencehe.SelectedIndexChanged += comboboxsifencehe_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 48;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 50;
            label7.Text = "快捷键";
            // 
            // checkboxsifencehe
            // 
            checkboxsifencehe.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsifencehe.Location = new Point(208, 184);
            checkboxsifencehe.Name = "checkboxsifencehe";
            checkboxsifencehe.Size = new Size(95, 23);
            checkboxsifencehe.TabIndex = 49;
            checkboxsifencehe.Text = "启用";
            checkboxsifencehe.CheckedChanged += checkboxsifencehe_CheckedChanged;
            // 
            // buttonsfchcz
            // 
            buttonsfchcz.BackHover = Color.Aquamarine;
            buttonsfchcz.DefaultBack = Color.Azure;
            buttonsfchcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonsfchcz.Location = new Point(208, 231);
            buttonsfchcz.Name = "buttonsfchcz";
            buttonsfchcz.Size = new Size(95, 64);
            buttonsfchcz.TabIndex = 59;
            buttonsfchcz.Text = "重置";
            buttonsfchcz.Click += buttonsfchcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标放在哪个位置就往哪个位置合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // sifencehe
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonsfchcz);
            Controls.Add(datagridviewsfch);
            Controls.Add(checkboxsfchtq);
            Controls.Add(textboxsfchjcfd);
            Controls.Add(label1);
            Controls.Add(comboboxsifencehe);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxsifencehe);
            FormBorderStyle = FormBorderStyle.None;
            Name = "sifencehe";
            Text = "sifencehe";
            Load += sifencehe_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewsfch).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewsfch;
        public AntdUI.Checkbox checkboxsfchtq;
        public Sunny.UI.UITextBox textboxsfchjcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxsifencehe;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxsifencehe;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonsfchcz;
        public AntdUI.Label label19;
    }
}