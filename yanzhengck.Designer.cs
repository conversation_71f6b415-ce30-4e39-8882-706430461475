﻿namespace ANYE_Balls
{
    partial class yanzhengck
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(yanzhengck));
            windowBar1 = new AntdUI.WindowBar();
            tabControl1 = new TabControl();
            tabPage1 = new TabPage();
            buttongy = new Button();
            buttonhuanbang = new Button();
            buttondenglu = new Button();
            checkBoxzidongdenglu = new CheckBox();
            checkBoxjizhumima = new CheckBox();
            textBoxmima = new TextBox();
            textBoxzhanghao = new TextBox();
            labelmima = new Label();
            labelzhanghao = new Label();
            groupBoxgonggao = new GroupBox();
            textBoxgonggao = new TextBox();
            tabPage2 = new TabPage();
            buttonzhuce = new Button();
            textBoxchongzhikami = new TextBox();
            labelchongzhikami = new Label();
            textBoxmima2 = new TextBox();
            textBoxzhanghao2 = new TextBox();
            labelmima2 = new Label();
            labelzhanghao2 = new Label();
            tabPage3 = new TabPage();
            buttonchongzhi = new Button();
            textBoxchongzhikami2 = new TextBox();
            labelchongzhikami2 = new Label();
            textBoxzhanghao3 = new TextBox();
            labelzhanghao3 = new Label();
            tabPage4 = new TabPage();
            tabControl1.SuspendLayout();
            tabPage1.SuspendLayout();
            groupBoxgonggao.SuspendLayout();
            tabPage2.SuspendLayout();
            tabPage3.SuspendLayout();
            SuspendLayout();
            // 
            // windowBar1
            // 
            windowBar1.BackColor = Color.Beige;
            windowBar1.Icon = (Image)resources.GetObject("windowBar1.Icon");
            windowBar1.Location = new Point(0, 0);
            windowBar1.MaximizeBox = false;
            windowBar1.Name = "windowBar1";
            windowBar1.Size = new Size(418, 29);
            windowBar1.TabIndex = 0;
            windowBar1.Text = "ANYE Balls 登录";
            windowBar1.Click += windowBar1_Click;
            // 
            // tabControl1
            // 
            tabControl1.Controls.Add(tabPage1);
            tabControl1.Controls.Add(tabPage2);
            tabControl1.Controls.Add(tabPage3);
            tabControl1.Controls.Add(tabPage4);
            tabControl1.Font = new Font("Microsoft YaHei UI", 10.5F, FontStyle.Regular, GraphicsUnit.Point);
            tabControl1.Location = new Point(12, 35);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(394, 294);
            tabControl1.TabIndex = 1;
            // 
            // tabPage1
            // 
            tabPage1.BackColor = Color.White;
            tabPage1.Controls.Add(buttongy);
            tabPage1.Controls.Add(buttonhuanbang);
            tabPage1.Controls.Add(buttondenglu);
            tabPage1.Controls.Add(checkBoxzidongdenglu);
            tabPage1.Controls.Add(checkBoxjizhumima);
            tabPage1.Controls.Add(textBoxmima);
            tabPage1.Controls.Add(textBoxzhanghao);
            tabPage1.Controls.Add(labelmima);
            tabPage1.Controls.Add(labelzhanghao);
            tabPage1.Controls.Add(groupBoxgonggao);
            tabPage1.Location = new Point(4, 29);
            tabPage1.Name = "tabPage1";
            tabPage1.Padding = new Padding(3);
            tabPage1.Size = new Size(386, 261);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "  登录";
            // 
            // buttongy
            // 
            buttongy.Location = new Point(320, 134);
            buttongy.Name = "buttongy";
            buttongy.Size = new Size(52, 118);
            buttongy.TabIndex = 19;
            buttongy.Text = "公益\r\n入口";
            buttongy.UseVisualStyleBackColor = true;
            buttongy.Visible = false;
            // 
            // buttonhuanbang
            // 
            buttonhuanbang.Location = new Point(199, 220);
            buttonhuanbang.Name = "buttonhuanbang";
            buttonhuanbang.Size = new Size(110, 32);
            buttonhuanbang.TabIndex = 18;
            buttonhuanbang.Text = "换      绑";
            buttonhuanbang.UseVisualStyleBackColor = true;
            buttonhuanbang.Click += buttonhuanbang_Click;
            // 
            // buttondenglu
            // 
            buttondenglu.Location = new Point(65, 220);
            buttondenglu.Name = "buttondenglu";
            buttondenglu.Size = new Size(110, 32);
            buttondenglu.TabIndex = 17;
            buttondenglu.Text = "登      录";
            buttondenglu.UseVisualStyleBackColor = true;
            buttondenglu.Click += buttondenglu_Click;
            // 
            // checkBoxzidongdenglu
            // 
            checkBoxzidongdenglu.AutoSize = true;
            checkBoxzidongdenglu.Location = new Point(215, 200);
            checkBoxzidongdenglu.Name = "checkBoxzidongdenglu";
            checkBoxzidongdenglu.Size = new Size(84, 24);
            checkBoxzidongdenglu.TabIndex = 16;
            checkBoxzidongdenglu.Text = "自动登录";
            checkBoxzidongdenglu.UseVisualStyleBackColor = true;
            checkBoxzidongdenglu.CheckedChanged += checkBoxzidongdenglu_CheckedChanged;
            // 
            // checkBoxjizhumima
            // 
            checkBoxjizhumima.AutoSize = true;
            checkBoxjizhumima.Location = new Point(79, 200);
            checkBoxjizhumima.Name = "checkBoxjizhumima";
            checkBoxjizhumima.Size = new Size(84, 24);
            checkBoxjizhumima.TabIndex = 15;
            checkBoxjizhumima.Text = "记住密码";
            checkBoxjizhumima.UseVisualStyleBackColor = true;
            checkBoxjizhumima.CheckedChanged += checkBoxjizhumima_CheckedChanged;
            // 
            // textBoxmima
            // 
            textBoxmima.Location = new Point(97, 171);
            textBoxmima.Name = "textBoxmima";
            textBoxmima.Size = new Size(217, 25);
            textBoxmima.TabIndex = 14;
            // 
            // textBoxzhanghao
            // 
            textBoxzhanghao.Location = new Point(97, 134);
            textBoxzhanghao.Name = "textBoxzhanghao";
            textBoxzhanghao.Size = new Size(217, 25);
            textBoxzhanghao.TabIndex = 13;
            // 
            // labelmima
            // 
            labelmima.AutoSize = true;
            labelmima.Location = new Point(53, 171);
            labelmima.Name = "labelmima";
            labelmima.Size = new Size(51, 20);
            labelmima.TabIndex = 12;
            labelmima.Text = "密码：";
            // 
            // labelzhanghao
            // 
            labelzhanghao.AutoSize = true;
            labelzhanghao.Location = new Point(53, 134);
            labelzhanghao.Name = "labelzhanghao";
            labelzhanghao.Size = new Size(51, 20);
            labelzhanghao.TabIndex = 11;
            labelzhanghao.Text = "账号：";
            // 
            // groupBoxgonggao
            // 
            groupBoxgonggao.BackColor = Color.White;
            groupBoxgonggao.Controls.Add(textBoxgonggao);
            groupBoxgonggao.Location = new Point(12, 6);
            groupBoxgonggao.Name = "groupBoxgonggao";
            groupBoxgonggao.Size = new Size(360, 115);
            groupBoxgonggao.TabIndex = 10;
            groupBoxgonggao.TabStop = false;
            groupBoxgonggao.Text = "公告";
            // 
            // textBoxgonggao
            // 
            textBoxgonggao.BackColor = Color.White;
            textBoxgonggao.BorderStyle = BorderStyle.None;
            textBoxgonggao.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point);
            textBoxgonggao.Location = new Point(16, 25);
            textBoxgonggao.Multiline = true;
            textBoxgonggao.Name = "textBoxgonggao";
            textBoxgonggao.ScrollBars = ScrollBars.Vertical;
            textBoxgonggao.Size = new Size(329, 74);
            textBoxgonggao.TabIndex = 0;
            // 
            // tabPage2
            // 
            tabPage2.BackColor = Color.White;
            tabPage2.Controls.Add(buttonzhuce);
            tabPage2.Controls.Add(textBoxchongzhikami);
            tabPage2.Controls.Add(labelchongzhikami);
            tabPage2.Controls.Add(textBoxmima2);
            tabPage2.Controls.Add(textBoxzhanghao2);
            tabPage2.Controls.Add(labelmima2);
            tabPage2.Controls.Add(labelzhanghao2);
            tabPage2.Location = new Point(4, 29);
            tabPage2.Name = "tabPage2";
            tabPage2.Padding = new Padding(3);
            tabPage2.Size = new Size(386, 261);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "  注册";
            // 
            // buttonzhuce
            // 
            buttonzhuce.Location = new Point(142, 215);
            buttonzhuce.Name = "buttonzhuce";
            buttonzhuce.Size = new Size(110, 32);
            buttonzhuce.TabIndex = 18;
            buttonzhuce.Text = "注      册";
            buttonzhuce.UseVisualStyleBackColor = true;
            buttonzhuce.Click += buttonzhuce_Click;
            // 
            // textBoxchongzhikami
            // 
            textBoxchongzhikami.BorderStyle = BorderStyle.FixedSingle;
            textBoxchongzhikami.Location = new Point(102, 84);
            textBoxchongzhikami.Multiline = true;
            textBoxchongzhikami.Name = "textBoxchongzhikami";
            textBoxchongzhikami.Size = new Size(217, 119);
            textBoxchongzhikami.TabIndex = 17;
            // 
            // labelchongzhikami
            // 
            labelchongzhikami.AutoSize = true;
            labelchongzhikami.Location = new Point(30, 84);
            labelchongzhikami.Name = "labelchongzhikami";
            labelchongzhikami.Size = new Size(79, 20);
            labelchongzhikami.TabIndex = 16;
            labelchongzhikami.Text = "充值卡密：";
            // 
            // textBoxmima2
            // 
            textBoxmima2.Location = new Point(102, 50);
            textBoxmima2.Name = "textBoxmima2";
            textBoxmima2.Size = new Size(217, 25);
            textBoxmima2.TabIndex = 15;
            // 
            // textBoxzhanghao2
            // 
            textBoxzhanghao2.Location = new Point(102, 16);
            textBoxzhanghao2.Name = "textBoxzhanghao2";
            textBoxzhanghao2.Size = new Size(217, 25);
            textBoxzhanghao2.TabIndex = 14;
            // 
            // labelmima2
            // 
            labelmima2.AutoSize = true;
            labelmima2.Location = new Point(58, 51);
            labelmima2.Name = "labelmima2";
            labelmima2.Size = new Size(51, 20);
            labelmima2.TabIndex = 13;
            labelmima2.Text = "密码：";
            // 
            // labelzhanghao2
            // 
            labelzhanghao2.AutoSize = true;
            labelzhanghao2.Location = new Point(58, 16);
            labelzhanghao2.Name = "labelzhanghao2";
            labelzhanghao2.Size = new Size(51, 20);
            labelzhanghao2.TabIndex = 12;
            labelzhanghao2.Text = "账号：";
            // 
            // tabPage3
            // 
            tabPage3.BackColor = Color.White;
            tabPage3.Controls.Add(buttonchongzhi);
            tabPage3.Controls.Add(textBoxchongzhikami2);
            tabPage3.Controls.Add(labelchongzhikami2);
            tabPage3.Controls.Add(textBoxzhanghao3);
            tabPage3.Controls.Add(labelzhanghao3);
            tabPage3.Location = new Point(4, 29);
            tabPage3.Name = "tabPage3";
            tabPage3.Size = new Size(386, 261);
            tabPage3.TabIndex = 2;
            tabPage3.Text = "  充值";
            // 
            // buttonchongzhi
            // 
            buttonchongzhi.Location = new Point(142, 215);
            buttonchongzhi.Name = "buttonchongzhi";
            buttonchongzhi.Size = new Size(110, 32);
            buttonchongzhi.TabIndex = 21;
            buttonchongzhi.Text = "充      值";
            buttonchongzhi.UseVisualStyleBackColor = true;
            buttonchongzhi.Click += buttonchongzhi_Click;
            // 
            // textBoxchongzhikami2
            // 
            textBoxchongzhikami2.BorderStyle = BorderStyle.FixedSingle;
            textBoxchongzhikami2.Location = new Point(102, 55);
            textBoxchongzhikami2.Multiline = true;
            textBoxchongzhikami2.Name = "textBoxchongzhikami2";
            textBoxchongzhikami2.Size = new Size(217, 148);
            textBoxchongzhikami2.TabIndex = 20;
            // 
            // labelchongzhikami2
            // 
            labelchongzhikami2.AutoSize = true;
            labelchongzhikami2.Location = new Point(30, 56);
            labelchongzhikami2.Name = "labelchongzhikami2";
            labelchongzhikami2.Size = new Size(79, 20);
            labelchongzhikami2.TabIndex = 19;
            labelchongzhikami2.Text = "充值卡密：";
            // 
            // textBoxzhanghao3
            // 
            textBoxzhanghao3.Location = new Point(102, 16);
            textBoxzhanghao3.Name = "textBoxzhanghao3";
            textBoxzhanghao3.Size = new Size(217, 25);
            textBoxzhanghao3.TabIndex = 18;
            // 
            // labelzhanghao3
            // 
            labelzhanghao3.AutoSize = true;
            labelzhanghao3.Location = new Point(58, 16);
            labelzhanghao3.Name = "labelzhanghao3";
            labelzhanghao3.Size = new Size(51, 20);
            labelzhanghao3.TabIndex = 17;
            labelzhanghao3.Text = "账号：";
            // 
            // tabPage4
            // 
            tabPage4.BackColor = Color.White;
            tabPage4.Location = new Point(4, 29);
            tabPage4.Name = "tabPage4";
            tabPage4.Size = new Size(386, 261);
            tabPage4.TabIndex = 3;
            tabPage4.Text = "修改密码";
            // 
            // yanzhengck
            // 
            AutoScaleDimensions = new SizeF(7F, 17F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.LightSlateGray;
            BackgroundImageLayout = ImageLayout.Stretch;
            ClientSize = new Size(418, 343);
            Controls.Add(tabControl1);
            Controls.Add(windowBar1);
            FormBorderStyle = FormBorderStyle.None;
            Icon = (Icon)resources.GetObject("$this.Icon");
            Name = "yanzhengck";
            Text = "yanzhengck";
            FormClosing += yanzhengck_FormClosing;
            Load += yanzhengck_Load;
            Shown += yanzhengck_Shown;
            tabControl1.ResumeLayout(false);
            tabPage1.ResumeLayout(false);
            tabPage1.PerformLayout();
            groupBoxgonggao.ResumeLayout(false);
            groupBoxgonggao.PerformLayout();
            tabPage2.ResumeLayout(false);
            tabPage2.PerformLayout();
            tabPage3.ResumeLayout(false);
            tabPage3.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private AntdUI.WindowBar windowBar1;
        private TabControl tabControl1;
        private TabPage tabPage1;
        private TabPage tabPage2;
        private TabPage tabPage3;
        private TabPage tabPage4;
        private Button buttongy;
        private Button buttonhuanbang;
        private Button buttondenglu;
        private CheckBox checkBoxzidongdenglu;
        private CheckBox checkBoxjizhumima;
        private TextBox textBoxmima;
        private TextBox textBoxzhanghao;
        private Label labelmima;
        private Label labelzhanghao;
        private GroupBox groupBoxgonggao;
        private TextBox textBoxgonggao;
        private Button buttonzhuce;
        private TextBox textBoxchongzhikami;
        private Label labelchongzhikami;
        private TextBox textBoxmima2;
        private TextBox textBoxzhanghao2;
        private Label labelmima2;
        private Label labelzhanghao2;
        private Button buttonchongzhi;
        private TextBox textBoxchongzhikami2;
        private Label labelchongzhikami2;
        private TextBox textBoxzhanghao3;
        private Label labelzhanghao3;
    }
}