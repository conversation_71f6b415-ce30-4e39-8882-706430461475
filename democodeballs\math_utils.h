#ifndef MATH_UTILS_H
#define MATH_UTILS_H

#include "common_defs.h"

/**
 * 计算向量的参数（模长和方向）
 * 
 * @param vector 要计算的向量
 */
void calculateVectorParameters(Vector* vector);

/**
 * 计算基于斜率和方向的参数
 * 
 * @param k 斜率
 * @param dx x方向的差值
 * @param r 半径/距离
 * @param sx 输出的x坐标
 * @param sy 输出的y坐标
 */
void calculateParameters(double k, double dx, double r, double* sx, double* sy);

/**
 * 计算鼠标指向的方向向量
 * 
 * @param mousePos 鼠标位置
 * @param windowRect 窗口矩形
 * @param dx 输出的x方向差值
 * @param dy 输出的y方向差值
 * @param k 输出的斜率
 * @param r 距离参数
 * @param sx 输出的x坐标
 * @param sy 输出的y坐标
 */
void calculateMouseDirection(Point mousePos, Rect windowRect, double* dx, double* dy, 
                             double* k, double r, double* sx, double* sy);

/**
 * 计算角度操作（旋转向量）
 * 
 * @param isPositive 是否为正向旋转
 * @param angle 旋转角度（度）
 * @param dwX 输出的x方向单位向量
 * @param dwY 输出的y方向单位向量
 * @param jiaodu 当前角度（会被修改）
 */
void calculateAngleOperation(bool isPositive, int angle, double* dwX, double* dwY, double* jiaodu);

/**
 * 计算旋转后的点坐标
 * 
 * @param x 原始x坐标
 * @param y 原始y坐标
 * @param angle 旋转角度（度）
 * @param outX 输出的旋转后x坐标
 * @param outY 输出的旋转后y坐标
 */
void rotatePoint(double x, double y, double angle, double* outX, double* outY);

/**
 * 根据窗口大小调整距离参数
 * 
 * @param distance 原始距离
 * @param windowRect 窗口矩形
 * @param joystickSize 摇杆大小系数
 * @return 调整后的距离
 */
int adjustDistance(int distance, Rect windowRect, float joystickSize);

#endif // MATH_UTILS_H 