﻿namespace ANYE_Balls
{
    partial class xuanzhuan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxxzjd4 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            textboxxzjcfd4 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            textboxxzjd3 = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxxzjcfd3 = new Sunny.UI.UITextBox();
            label6 = new AntdUI.Label();
            textboxxzjd2 = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            textboxxzjcfd2 = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxxzjd1 = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            datagridviewxz = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxxztq = new AntdUI.Checkbox();
            textboxxzjcfd1 = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxxuanzhuan = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxxuanzhuan = new AntdUI.Checkbox();
            buttonxzcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewxz).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxxzjd4
            // 
            textboxxzjd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjd4.Location = new Point(125, 469);
            textboxxzjd4.Margin = new Padding(4, 5, 4, 5);
            textboxxzjd4.MinimumSize = new Size(1, 16);
            textboxxzjd4.Name = "textboxxzjd4";
            textboxxzjd4.Padding = new Padding(5);
            textboxxzjd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjd4.ShowText = false;
            textboxxzjd4.Size = new Size(70, 30);
            textboxxzjd4.TabIndex = 120;
            textboxxzjd4.Text = "0";
            textboxxzjd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjd4.Watermark = "";
            textboxxzjd4.TextChanged += textboxxzjd4_TextChanged;
            textboxxzjd4.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(60, 473);
            label8.Name = "label8";
            label8.Size = new Size(68, 23);
            label8.TabIndex = 119;
            label8.Text = "角度4";
            // 
            // textboxxzjcfd4
            // 
            textboxxzjcfd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjcfd4.Location = new Point(125, 435);
            textboxxzjcfd4.Margin = new Padding(4, 5, 4, 5);
            textboxxzjcfd4.MinimumSize = new Size(1, 16);
            textboxxzjcfd4.Name = "textboxxzjcfd4";
            textboxxzjcfd4.Padding = new Padding(5);
            textboxxzjcfd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjcfd4.ShowText = false;
            textboxxzjcfd4.Size = new Size(70, 30);
            textboxxzjcfd4.TabIndex = 118;
            textboxxzjcfd4.Text = "0";
            textboxxzjcfd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjcfd4.Watermark = "";
            textboxxzjcfd4.TextChanged += textboxxzjcfd4_TextChanged;
            textboxxzjcfd4.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 439);
            label9.Name = "label9";
            label9.Size = new Size(109, 23);
            label9.TabIndex = 117;
            label9.Text = "交叉幅度4";
            // 
            // textboxxzjd3
            // 
            textboxxzjd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjd3.Location = new Point(125, 401);
            textboxxzjd3.Margin = new Padding(4, 5, 4, 5);
            textboxxzjd3.MinimumSize = new Size(1, 16);
            textboxxzjd3.Name = "textboxxzjd3";
            textboxxzjd3.Padding = new Padding(5);
            textboxxzjd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjd3.ShowText = false;
            textboxxzjd3.Size = new Size(70, 30);
            textboxxzjd3.TabIndex = 116;
            textboxxzjd3.Text = "0";
            textboxxzjd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjd3.Watermark = "";
            textboxxzjd3.TextChanged += textboxxzjd3_TextChanged;
            textboxxzjd3.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(60, 405);
            label5.Name = "label5";
            label5.Size = new Size(68, 23);
            label5.TabIndex = 115;
            label5.Text = "角度3";
            // 
            // textboxxzjcfd3
            // 
            textboxxzjcfd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjcfd3.Location = new Point(125, 367);
            textboxxzjcfd3.Margin = new Padding(4, 5, 4, 5);
            textboxxzjcfd3.MinimumSize = new Size(1, 16);
            textboxxzjcfd3.Name = "textboxxzjcfd3";
            textboxxzjcfd3.Padding = new Padding(5);
            textboxxzjcfd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjcfd3.ShowText = false;
            textboxxzjcfd3.Size = new Size(70, 30);
            textboxxzjcfd3.TabIndex = 114;
            textboxxzjcfd3.Text = "0";
            textboxxzjcfd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjcfd3.Watermark = "";
            textboxxzjcfd3.TextChanged += textboxxzjcfd3_TextChanged;
            textboxxzjcfd3.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(19, 371);
            label6.Name = "label6";
            label6.Size = new Size(109, 23);
            label6.TabIndex = 113;
            label6.Text = "交叉幅度3";
            // 
            // textboxxzjd2
            // 
            textboxxzjd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjd2.Location = new Point(125, 333);
            textboxxzjd2.Margin = new Padding(4, 5, 4, 5);
            textboxxzjd2.MinimumSize = new Size(1, 16);
            textboxxzjd2.Name = "textboxxzjd2";
            textboxxzjd2.Padding = new Padding(5);
            textboxxzjd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjd2.ShowText = false;
            textboxxzjd2.Size = new Size(70, 30);
            textboxxzjd2.TabIndex = 112;
            textboxxzjd2.Text = "0";
            textboxxzjd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjd2.Watermark = "";
            textboxxzjd2.TextChanged += textboxxzjd2_TextChanged;
            textboxxzjd2.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(60, 337);
            label3.Name = "label3";
            label3.Size = new Size(68, 23);
            label3.TabIndex = 111;
            label3.Text = "角度2";
            // 
            // textboxxzjcfd2
            // 
            textboxxzjcfd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjcfd2.Location = new Point(125, 299);
            textboxxzjcfd2.Margin = new Padding(4, 5, 4, 5);
            textboxxzjcfd2.MinimumSize = new Size(1, 16);
            textboxxzjcfd2.Name = "textboxxzjcfd2";
            textboxxzjcfd2.Padding = new Padding(5);
            textboxxzjcfd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjcfd2.ShowText = false;
            textboxxzjcfd2.Size = new Size(70, 30);
            textboxxzjcfd2.TabIndex = 110;
            textboxxzjcfd2.Text = "0";
            textboxxzjcfd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjcfd2.Watermark = "";
            textboxxzjcfd2.TextChanged += textboxxzjcfd2_TextChanged;
            textboxxzjcfd2.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 303);
            label4.Name = "label4";
            label4.Size = new Size(109, 23);
            label4.TabIndex = 109;
            label4.Text = "交叉幅度2";
            // 
            // textboxxzjd1
            // 
            textboxxzjd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjd1.Location = new Point(125, 265);
            textboxxzjd1.Margin = new Padding(4, 5, 4, 5);
            textboxxzjd1.MinimumSize = new Size(1, 16);
            textboxxzjd1.Name = "textboxxzjd1";
            textboxxzjd1.Padding = new Padding(5);
            textboxxzjd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjd1.ShowText = false;
            textboxxzjd1.Size = new Size(70, 30);
            textboxxzjd1.TabIndex = 108;
            textboxxzjd1.Text = "0";
            textboxxzjd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjd1.Watermark = "";
            textboxxzjd1.TextChanged += textboxxzjd1_TextChanged;
            textboxxzjd1.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(60, 269);
            label2.Name = "label2";
            label2.Size = new Size(68, 23);
            label2.TabIndex = 107;
            label2.Text = "角度1";
            // 
            // datagridviewxz
            // 
            datagridviewxz.AllowUserToAddRows = false;
            datagridviewxz.AllowUserToDeleteRows = false;
            datagridviewxz.AllowUserToResizeColumns = false;
            datagridviewxz.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewxz.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewxz.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewxz.BorderStyle = BorderStyle.Fixed3D;
            datagridviewxz.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewxz.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewxz.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewxz.ColumnHeadersHeight = 40;
            datagridviewxz.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewxz.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewxz.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewxz.EnableHeadersVisualStyles = false;
            datagridviewxz.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewxz.GridColor = Color.AliceBlue;
            datagridviewxz.Location = new Point(309, 184);
            datagridviewxz.Name = "datagridviewxz";
            datagridviewxz.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewxz.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewxz.RowHeadersVisible = false;
            datagridviewxz.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewxz.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewxz.RowTemplate.Height = 25;
            datagridviewxz.ScrollBars = ScrollBars.None;
            datagridviewxz.SelectedIndex = -1;
            datagridviewxz.Size = new Size(294, 322);
            datagridviewxz.TabIndex = 106;
            datagridviewxz.CellValidating += datagridviewxz_CellValidating;
            datagridviewxz.CellValueChanged += datagridviewxz_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxxztq
            // 
            checkboxxztq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxxztq.Location = new Point(607, 186);
            checkboxxztq.Name = "checkboxxztq";
            checkboxxztq.Size = new Size(131, 23);
            checkboxxztq.TabIndex = 105;
            checkboxxztq.Text = "自动吐球";
            checkboxxztq.CheckedChanged += checkboxxztq_CheckedChanged;
            // 
            // textboxxzjcfd1
            // 
            textboxxzjcfd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxxzjcfd1.Location = new Point(125, 231);
            textboxxzjcfd1.Margin = new Padding(4, 5, 4, 5);
            textboxxzjcfd1.MinimumSize = new Size(1, 16);
            textboxxzjcfd1.Name = "textboxxzjcfd1";
            textboxxzjcfd1.Padding = new Padding(5);
            textboxxzjcfd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxxzjcfd1.ShowText = false;
            textboxxzjcfd1.Size = new Size(70, 30);
            textboxxzjcfd1.TabIndex = 104;
            textboxxzjcfd1.Text = "0";
            textboxxzjcfd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxxzjcfd1.Watermark = "";
            textboxxzjcfd1.TextChanged += textboxxzjcfd1_TextChanged;
            textboxxzjcfd1.KeyPress += textboxxzjcfd1_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(19, 235);
            label1.Name = "label1";
            label1.Size = new Size(109, 23);
            label1.TabIndex = 103;
            label1.Text = "交叉幅度1";
            // 
            // comboboxxuanzhuan
            // 
            comboboxxuanzhuan.DataSource = null;
            comboboxxuanzhuan.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxxuanzhuan.DropDownWidth = 70;
            comboboxxuanzhuan.FillColor = Color.White;
            comboboxxuanzhuan.FillColorGradient = true;
            comboboxxuanzhuan.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxxuanzhuan.ItemHeight = 40;
            comboboxxuanzhuan.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxxuanzhuan.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxxuanzhuan.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxxuanzhuan.Location = new Point(125, 180);
            comboboxxuanzhuan.Margin = new Padding(0);
            comboboxxuanzhuan.MaxDropDownItems = 30;
            comboboxxuanzhuan.MinimumSize = new Size(63, 0);
            comboboxxuanzhuan.Name = "comboboxxuanzhuan";
            comboboxxuanzhuan.Padding = new Padding(0, 0, 30, 10);
            comboboxxuanzhuan.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxxuanzhuan.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxxuanzhuan.ScrollBarStyleInherited = false;
            comboboxxuanzhuan.Size = new Size(70, 31);
            comboboxxuanzhuan.SymbolSize = 24;
            comboboxxuanzhuan.TabIndex = 102;
            comboboxxuanzhuan.Text = "A";
            comboboxxuanzhuan.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxxuanzhuan.Watermark = "";
            comboboxxuanzhuan.SelectedIndexChanged += comboboxxuanzhuan_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 99;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 101;
            label7.Text = "快捷键";
            // 
            // checkboxxuanzhuan
            // 
            checkboxxuanzhuan.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxxuanzhuan.Location = new Point(208, 184);
            checkboxxuanzhuan.Name = "checkboxxuanzhuan";
            checkboxxuanzhuan.Size = new Size(95, 23);
            checkboxxuanzhuan.TabIndex = 100;
            checkboxxuanzhuan.Text = "启用";
            checkboxxuanzhuan.CheckedChanged += checkboxxuanzhuan_CheckedChanged;
            // 
            // buttonxzcz
            // 
            buttonxzcz.BackHover = Color.Aquamarine;
            buttonxzcz.DefaultBack = Color.Azure;
            buttonxzcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonxzcz.Location = new Point(208, 231);
            buttonxzcz.Name = "buttonxzcz";
            buttonxzcz.Size = new Size(95, 64);
            buttonxzcz.TabIndex = 121;
            buttonxzcz.Text = "重置";
            buttonxzcz.Click += buttonxzcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 55;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标相对于遥杆哪边就往哪边旋";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // xuanzhuan
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonxzcz);
            Controls.Add(textboxxzjd4);
            Controls.Add(label8);
            Controls.Add(textboxxzjcfd4);
            Controls.Add(label9);
            Controls.Add(textboxxzjd3);
            Controls.Add(label5);
            Controls.Add(textboxxzjcfd3);
            Controls.Add(label6);
            Controls.Add(textboxxzjd2);
            Controls.Add(label3);
            Controls.Add(textboxxzjcfd2);
            Controls.Add(label4);
            Controls.Add(textboxxzjd1);
            Controls.Add(label2);
            Controls.Add(datagridviewxz);
            Controls.Add(checkboxxztq);
            Controls.Add(textboxxzjcfd1);
            Controls.Add(label1);
            Controls.Add(comboboxxuanzhuan);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxxuanzhuan);
            FormBorderStyle = FormBorderStyle.None;
            Name = "xuanzhuan";
            Text = "xuanzhuan";
            Load += xuanzhuan_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewxz).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewxz;
        public Sunny.UI.UITextBox textboxxzjd4;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxxzjcfd4;
        public AntdUI.Label label9;
        public Sunny.UI.UITextBox textboxxzjd3;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxxzjcfd3;
        public AntdUI.Label label6;
        public Sunny.UI.UITextBox textboxxzjd2;
        public AntdUI.Label label3;
        public Sunny.UI.UITextBox textboxxzjcfd2;
        public AntdUI.Label label4;
        public Sunny.UI.UITextBox textboxxzjd1;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxxztq;
        public Sunny.UI.UITextBox textboxxzjcfd1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxxuanzhuan;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxxuanzhuan;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonxzcz;
        public AntdUI.Label label19;
    }
}