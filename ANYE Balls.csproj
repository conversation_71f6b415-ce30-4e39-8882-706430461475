﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <RootNamespace>ANYE_Balls</RootNamespace>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
    <ApplicationIcon>Gemini.ico</ApplicationIcon>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="Gemini.ico" />
  </ItemGroup>

  <ItemGroup>
    <None Include="bin\Release\net6.0-windows\publish\解断\ANYE解断.exe" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AntdUI" Version="1.3.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="ReaLTaiizor" Version="3.8.0.5" />
    <PackageReference Include="sharpadbclient" Version="2.3.23" />
    <PackageReference Include="SunnyUI" Version="3.6.6" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>