﻿namespace ANYE_Balls
{
    partial class ncsanjiao2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewncsj2 = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxncsj2jt = new AntdUI.Checkbox();
            checkboxncsj2tq = new AntdUI.Checkbox();
            textboxncsj2jd = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            textboxncsj2jcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxncsanjiao2 = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxncsanjiao2 = new AntdUI.Checkbox();
            buttonncsj2cz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewncsj2).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewncsj2
            // 
            datagridviewncsj2.AllowUserToAddRows = false;
            datagridviewncsj2.AllowUserToDeleteRows = false;
            datagridviewncsj2.AllowUserToResizeColumns = false;
            datagridviewncsj2.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewncsj2.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewncsj2.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewncsj2.BorderStyle = BorderStyle.Fixed3D;
            datagridviewncsj2.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewncsj2.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewncsj2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewncsj2.ColumnHeadersHeight = 40;
            datagridviewncsj2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewncsj2.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewncsj2.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewncsj2.EnableHeadersVisualStyles = false;
            datagridviewncsj2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewncsj2.GridColor = Color.AliceBlue;
            datagridviewncsj2.Location = new Point(309, 184);
            datagridviewncsj2.Name = "datagridviewncsj2";
            datagridviewncsj2.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewncsj2.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewncsj2.RowHeadersVisible = false;
            datagridviewncsj2.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewncsj2.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewncsj2.RowTemplate.Height = 25;
            datagridviewncsj2.ScrollBars = ScrollBars.None;
            datagridviewncsj2.SelectedIndex = -1;
            datagridviewncsj2.Size = new Size(294, 300);
            datagridviewncsj2.TabIndex = 58;
            datagridviewncsj2.CellValidating += datagridviewncsj2_CellValidating;
            datagridviewncsj2.CellValueChanged += datagridviewncsj2_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxncsj2jt
            // 
            checkboxncsj2jt.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsj2jt.Location = new Point(607, 215);
            checkboxncsj2jt.Name = "checkboxncsj2jt";
            checkboxncsj2jt.Size = new Size(167, 23);
            checkboxncsj2jt.TabIndex = 57;
            checkboxncsj2jt.Text = "箭头方向为准";
            checkboxncsj2jt.CheckedChanged += checkboxncsj2jt_CheckedChanged;
            // 
            // checkboxncsj2tq
            // 
            checkboxncsj2tq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsj2tq.Location = new Point(607, 186);
            checkboxncsj2tq.Name = "checkboxncsj2tq";
            checkboxncsj2tq.Size = new Size(131, 23);
            checkboxncsj2tq.TabIndex = 56;
            checkboxncsj2tq.Text = "自动吐球";
            checkboxncsj2tq.CheckedChanged += checkboxncsj2tq_CheckedChanged;
            // 
            // textboxncsj2jd
            // 
            textboxncsj2jd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncsj2jd.Location = new Point(125, 265);
            textboxncsj2jd.Margin = new Padding(4, 5, 4, 5);
            textboxncsj2jd.MinimumSize = new Size(1, 16);
            textboxncsj2jd.Name = "textboxncsj2jd";
            textboxncsj2jd.Padding = new Padding(5);
            textboxncsj2jd.RectColor = Color.FromArgb(255, 255, 192);
            textboxncsj2jd.ShowText = false;
            textboxncsj2jd.Size = new Size(70, 30);
            textboxncsj2jd.TabIndex = 55;
            textboxncsj2jd.Text = "0";
            textboxncsj2jd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncsj2jd.Watermark = "";
            textboxncsj2jd.TextChanged += textboxncsj2jd_TextChanged;
            textboxncsj2jd.KeyPress += textboxncsj2jd_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(71, 269);
            label2.Name = "label2";
            label2.Size = new Size(57, 23);
            label2.TabIndex = 54;
            label2.Text = "角度";
            // 
            // textboxncsj2jcfd
            // 
            textboxncsj2jcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncsj2jcfd.Location = new Point(125, 231);
            textboxncsj2jcfd.Margin = new Padding(4, 5, 4, 5);
            textboxncsj2jcfd.MinimumSize = new Size(1, 16);
            textboxncsj2jcfd.Name = "textboxncsj2jcfd";
            textboxncsj2jcfd.Padding = new Padding(5);
            textboxncsj2jcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxncsj2jcfd.ShowText = false;
            textboxncsj2jcfd.Size = new Size(70, 30);
            textboxncsj2jcfd.TabIndex = 53;
            textboxncsj2jcfd.Text = "0";
            textboxncsj2jcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncsj2jcfd.Watermark = "";
            textboxncsj2jcfd.TextChanged += textboxncsj2jcfd_TextChanged;
            textboxncsj2jcfd.KeyPress += textboxncsj2jcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 52;
            label1.Text = "交叉幅度";
            // 
            // comboboxncsanjiao2
            // 
            comboboxncsanjiao2.DataSource = null;
            comboboxncsanjiao2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxncsanjiao2.DropDownWidth = 70;
            comboboxncsanjiao2.FillColor = Color.White;
            comboboxncsanjiao2.FillColorGradient = true;
            comboboxncsanjiao2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxncsanjiao2.ItemHeight = 40;
            comboboxncsanjiao2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxncsanjiao2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxncsanjiao2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxncsanjiao2.Location = new Point(125, 180);
            comboboxncsanjiao2.Margin = new Padding(0);
            comboboxncsanjiao2.MaxDropDownItems = 30;
            comboboxncsanjiao2.MinimumSize = new Size(63, 0);
            comboboxncsanjiao2.Name = "comboboxncsanjiao2";
            comboboxncsanjiao2.Padding = new Padding(0, 0, 30, 10);
            comboboxncsanjiao2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxncsanjiao2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxncsanjiao2.ScrollBarStyleInherited = false;
            comboboxncsanjiao2.Size = new Size(70, 31);
            comboboxncsanjiao2.SymbolSize = 24;
            comboboxncsanjiao2.TabIndex = 51;
            comboboxncsanjiao2.Text = "A";
            comboboxncsanjiao2.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxncsanjiao2.Watermark = "";
            comboboxncsanjiao2.SelectedIndexChanged += comboboxncsanjiao2_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 48;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 50;
            label7.Text = "快捷键";
            // 
            // checkboxncsanjiao2
            // 
            checkboxncsanjiao2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsanjiao2.Location = new Point(208, 184);
            checkboxncsanjiao2.Name = "checkboxncsanjiao2";
            checkboxncsanjiao2.Size = new Size(95, 23);
            checkboxncsanjiao2.TabIndex = 49;
            checkboxncsanjiao2.Text = "启用";
            checkboxncsanjiao2.CheckedChanged += checkboxncsanjiao2_CheckedChanged;
            // 
            // buttonncsj2cz
            // 
            buttonncsj2cz.BackHover = Color.Aquamarine;
            buttonncsj2cz.DefaultBack = Color.Azure;
            buttonncsj2cz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonncsj2cz.Location = new Point(208, 231);
            buttonncsj2cz.Name = "buttonncsj2cz";
            buttonncsj2cz.Size = new Size(95, 64);
            buttonncsj2cz.TabIndex = 146;
            buttonncsj2cz.Text = "重置";
            buttonncsj2cz.Click += buttonncsj2cz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "鼠标方向：鼠标放在哪里往哪里合\r\n箭头方向：遥杆指向哪里往哪里合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // ncsanjiao2
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonncsj2cz);
            Controls.Add(datagridviewncsj2);
            Controls.Add(checkboxncsj2jt);
            Controls.Add(checkboxncsj2tq);
            Controls.Add(textboxncsj2jd);
            Controls.Add(label2);
            Controls.Add(textboxncsj2jcfd);
            Controls.Add(label1);
            Controls.Add(comboboxncsanjiao2);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxncsanjiao2);
            FormBorderStyle = FormBorderStyle.None;
            Name = "ncsanjiao2";
            Text = "ncsanjiao2";
            Load += ncsanjiao2_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewncsj2).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewncsj2;
        public AntdUI.Checkbox checkboxncsj2jt;
        public AntdUI.Checkbox checkboxncsj2tq;
        public Sunny.UI.UITextBox textboxncsj2jd;
        public AntdUI.Label label2;
        public Sunny.UI.UITextBox textboxncsj2jcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxncsanjiao2;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxncsanjiao2;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonncsj2cz;
        public AntdUI.Label label19;
    }
}