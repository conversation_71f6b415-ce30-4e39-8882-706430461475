﻿namespace ANYE_Balls
{
    partial class ncxuanzhuan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxncxzjd4 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            textboxncxzjcfd4 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            textboxncxzjd3 = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxncxzjcfd3 = new Sunny.UI.UITextBox();
            label6 = new AntdUI.Label();
            textboxncxzjd2 = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            textboxncxzjcfd2 = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxncxzjd1 = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            datagridviewncxz = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxncxztq = new AntdUI.Checkbox();
            textboxncxzjcfd1 = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxncxuanzhuan = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxncxuanzhuan = new AntdUI.Checkbox();
            buttonncxzcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewncxz).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxncxzjd4
            // 
            textboxncxzjd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjd4.Location = new Point(125, 469);
            textboxncxzjd4.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjd4.MinimumSize = new Size(1, 16);
            textboxncxzjd4.Name = "textboxncxzjd4";
            textboxncxzjd4.Padding = new Padding(5);
            textboxncxzjd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjd4.ShowText = false;
            textboxncxzjd4.Size = new Size(70, 30);
            textboxncxzjd4.TabIndex = 142;
            textboxncxzjd4.Text = "0";
            textboxncxzjd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjd4.Watermark = "";
            textboxncxzjd4.TextChanged += textboxncxzjd4_TextChanged;
            textboxncxzjd4.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(60, 473);
            label8.Name = "label8";
            label8.Size = new Size(68, 23);
            label8.TabIndex = 141;
            label8.Text = "角度4";
            // 
            // textboxncxzjcfd4
            // 
            textboxncxzjcfd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjcfd4.Location = new Point(125, 435);
            textboxncxzjcfd4.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjcfd4.MinimumSize = new Size(1, 16);
            textboxncxzjcfd4.Name = "textboxncxzjcfd4";
            textboxncxzjcfd4.Padding = new Padding(5);
            textboxncxzjcfd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjcfd4.ShowText = false;
            textboxncxzjcfd4.Size = new Size(70, 30);
            textboxncxzjcfd4.TabIndex = 140;
            textboxncxzjcfd4.Text = "0";
            textboxncxzjcfd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjcfd4.Watermark = "";
            textboxncxzjcfd4.TextChanged += textboxncxzjcfd4_TextChanged;
            textboxncxzjcfd4.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 439);
            label9.Name = "label9";
            label9.Size = new Size(109, 23);
            label9.TabIndex = 139;
            label9.Text = "交叉幅度4";
            // 
            // textboxncxzjd3
            // 
            textboxncxzjd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjd3.Location = new Point(125, 401);
            textboxncxzjd3.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjd3.MinimumSize = new Size(1, 16);
            textboxncxzjd3.Name = "textboxncxzjd3";
            textboxncxzjd3.Padding = new Padding(5);
            textboxncxzjd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjd3.ShowText = false;
            textboxncxzjd3.Size = new Size(70, 30);
            textboxncxzjd3.TabIndex = 138;
            textboxncxzjd3.Text = "0";
            textboxncxzjd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjd3.Watermark = "";
            textboxncxzjd3.TextChanged += textboxncxzjd3_TextChanged;
            textboxncxzjd3.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(60, 405);
            label5.Name = "label5";
            label5.Size = new Size(68, 23);
            label5.TabIndex = 137;
            label5.Text = "角度3";
            // 
            // textboxncxzjcfd3
            // 
            textboxncxzjcfd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjcfd3.Location = new Point(125, 367);
            textboxncxzjcfd3.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjcfd3.MinimumSize = new Size(1, 16);
            textboxncxzjcfd3.Name = "textboxncxzjcfd3";
            textboxncxzjcfd3.Padding = new Padding(5);
            textboxncxzjcfd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjcfd3.ShowText = false;
            textboxncxzjcfd3.Size = new Size(70, 30);
            textboxncxzjcfd3.TabIndex = 136;
            textboxncxzjcfd3.Text = "0";
            textboxncxzjcfd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjcfd3.Watermark = "";
            textboxncxzjcfd3.TextChanged += textboxncxzjcfd3_TextChanged;
            textboxncxzjcfd3.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(19, 371);
            label6.Name = "label6";
            label6.Size = new Size(109, 23);
            label6.TabIndex = 135;
            label6.Text = "交叉幅度3";
            // 
            // textboxncxzjd2
            // 
            textboxncxzjd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjd2.Location = new Point(125, 333);
            textboxncxzjd2.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjd2.MinimumSize = new Size(1, 16);
            textboxncxzjd2.Name = "textboxncxzjd2";
            textboxncxzjd2.Padding = new Padding(5);
            textboxncxzjd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjd2.ShowText = false;
            textboxncxzjd2.Size = new Size(70, 30);
            textboxncxzjd2.TabIndex = 134;
            textboxncxzjd2.Text = "0";
            textboxncxzjd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjd2.Watermark = "";
            textboxncxzjd2.TextChanged += textboxncxzjd2_TextChanged;
            textboxncxzjd2.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(60, 337);
            label3.Name = "label3";
            label3.Size = new Size(68, 23);
            label3.TabIndex = 133;
            label3.Text = "角度2";
            // 
            // textboxncxzjcfd2
            // 
            textboxncxzjcfd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjcfd2.Location = new Point(125, 299);
            textboxncxzjcfd2.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjcfd2.MinimumSize = new Size(1, 16);
            textboxncxzjcfd2.Name = "textboxncxzjcfd2";
            textboxncxzjcfd2.Padding = new Padding(5);
            textboxncxzjcfd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjcfd2.ShowText = false;
            textboxncxzjcfd2.Size = new Size(70, 30);
            textboxncxzjcfd2.TabIndex = 132;
            textboxncxzjcfd2.Text = "0";
            textboxncxzjcfd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjcfd2.Watermark = "";
            textboxncxzjcfd2.TextChanged += textboxncxzjcfd2_TextChanged;
            textboxncxzjcfd2.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 303);
            label4.Name = "label4";
            label4.Size = new Size(109, 23);
            label4.TabIndex = 131;
            label4.Text = "交叉幅度2";
            // 
            // textboxncxzjd1
            // 
            textboxncxzjd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjd1.Location = new Point(125, 265);
            textboxncxzjd1.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjd1.MinimumSize = new Size(1, 16);
            textboxncxzjd1.Name = "textboxncxzjd1";
            textboxncxzjd1.Padding = new Padding(5);
            textboxncxzjd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjd1.ShowText = false;
            textboxncxzjd1.Size = new Size(70, 30);
            textboxncxzjd1.TabIndex = 130;
            textboxncxzjd1.Text = "0";
            textboxncxzjd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjd1.Watermark = "";
            textboxncxzjd1.TextChanged += textboxncxzjd1_TextChanged;
            textboxncxzjd1.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(60, 269);
            label2.Name = "label2";
            label2.Size = new Size(68, 23);
            label2.TabIndex = 129;
            label2.Text = "角度1";
            // 
            // datagridviewncxz
            // 
            datagridviewncxz.AllowUserToAddRows = false;
            datagridviewncxz.AllowUserToDeleteRows = false;
            datagridviewncxz.AllowUserToResizeColumns = false;
            datagridviewncxz.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewncxz.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewncxz.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewncxz.BorderStyle = BorderStyle.Fixed3D;
            datagridviewncxz.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewncxz.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewncxz.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewncxz.ColumnHeadersHeight = 40;
            datagridviewncxz.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewncxz.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewncxz.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewncxz.EnableHeadersVisualStyles = false;
            datagridviewncxz.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewncxz.GridColor = Color.AliceBlue;
            datagridviewncxz.Location = new Point(309, 184);
            datagridviewncxz.Name = "datagridviewncxz";
            datagridviewncxz.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewncxz.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewncxz.RowHeadersVisible = false;
            datagridviewncxz.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewncxz.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewncxz.RowTemplate.Height = 25;
            datagridviewncxz.ScrollBars = ScrollBars.None;
            datagridviewncxz.SelectedIndex = -1;
            datagridviewncxz.Size = new Size(294, 322);
            datagridviewncxz.TabIndex = 128;
            datagridviewncxz.CellValidating += datagridviewncxz_CellValidating;
            datagridviewncxz.CellValueChanged += datagridviewncxz_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.Programmatic;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.Programmatic;
            Column2.Width = 146;
            // 
            // checkboxncxztq
            // 
            checkboxncxztq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncxztq.Location = new Point(607, 186);
            checkboxncxztq.Name = "checkboxncxztq";
            checkboxncxztq.Size = new Size(131, 23);
            checkboxncxztq.TabIndex = 127;
            checkboxncxztq.Text = "自动吐球";
            checkboxncxztq.CheckedChanged += checkboxncxztq_CheckedChanged;
            // 
            // textboxncxzjcfd1
            // 
            textboxncxzjcfd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncxzjcfd1.Location = new Point(125, 231);
            textboxncxzjcfd1.Margin = new Padding(4, 5, 4, 5);
            textboxncxzjcfd1.MinimumSize = new Size(1, 16);
            textboxncxzjcfd1.Name = "textboxncxzjcfd1";
            textboxncxzjcfd1.Padding = new Padding(5);
            textboxncxzjcfd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxncxzjcfd1.ShowText = false;
            textboxncxzjcfd1.Size = new Size(70, 30);
            textboxncxzjcfd1.TabIndex = 126;
            textboxncxzjcfd1.Text = "0";
            textboxncxzjcfd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncxzjcfd1.Watermark = "";
            textboxncxzjcfd1.TextChanged += textboxncxzjcfd1_TextChanged;
            textboxncxzjcfd1.KeyPress += textboxncxzjcfd1_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(19, 235);
            label1.Name = "label1";
            label1.Size = new Size(109, 23);
            label1.TabIndex = 125;
            label1.Text = "交叉幅度1";
            // 
            // comboboxncxuanzhuan
            // 
            comboboxncxuanzhuan.DataSource = null;
            comboboxncxuanzhuan.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxncxuanzhuan.DropDownWidth = 70;
            comboboxncxuanzhuan.FillColor = Color.White;
            comboboxncxuanzhuan.FillColorGradient = true;
            comboboxncxuanzhuan.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxncxuanzhuan.ItemHeight = 40;
            comboboxncxuanzhuan.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxncxuanzhuan.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxncxuanzhuan.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxncxuanzhuan.Location = new Point(125, 180);
            comboboxncxuanzhuan.Margin = new Padding(0);
            comboboxncxuanzhuan.MaxDropDownItems = 30;
            comboboxncxuanzhuan.MinimumSize = new Size(63, 0);
            comboboxncxuanzhuan.Name = "comboboxncxuanzhuan";
            comboboxncxuanzhuan.Padding = new Padding(0, 0, 30, 10);
            comboboxncxuanzhuan.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxncxuanzhuan.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxncxuanzhuan.ScrollBarStyleInherited = false;
            comboboxncxuanzhuan.Size = new Size(70, 31);
            comboboxncxuanzhuan.SymbolSize = 24;
            comboboxncxuanzhuan.TabIndex = 124;
            comboboxncxuanzhuan.Text = "A";
            comboboxncxuanzhuan.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxncxuanzhuan.Watermark = "";
            comboboxncxuanzhuan.SelectedIndexChanged += comboboxncxuanzhuan_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 121;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 123;
            label7.Text = "快捷键";
            // 
            // checkboxncxuanzhuan
            // 
            checkboxncxuanzhuan.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncxuanzhuan.Location = new Point(208, 184);
            checkboxncxuanzhuan.Name = "checkboxncxuanzhuan";
            checkboxncxuanzhuan.Size = new Size(95, 23);
            checkboxncxuanzhuan.TabIndex = 122;
            checkboxncxuanzhuan.Text = "启用";
            checkboxncxuanzhuan.CheckedChanged += checkboxncxuanzhuan_CheckedChanged;
            // 
            // buttonncxzcz
            // 
            buttonncxzcz.BackHover = Color.Aquamarine;
            buttonncxzcz.DefaultBack = Color.Azure;
            buttonncxzcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonncxzcz.Location = new Point(208, 231);
            buttonncxzcz.Name = "buttonncxzcz";
            buttonncxzcz.Size = new Size(95, 64);
            buttonncxzcz.TabIndex = 146;
            buttonncxzcz.Text = "重置";
            buttonncxzcz.Click += buttonncxzcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 55;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标相对于遥杆哪边就往哪边旋";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // ncxuanzhuan
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonncxzcz);
            Controls.Add(textboxncxzjd4);
            Controls.Add(label8);
            Controls.Add(textboxncxzjcfd4);
            Controls.Add(label9);
            Controls.Add(textboxncxzjd3);
            Controls.Add(label5);
            Controls.Add(textboxncxzjcfd3);
            Controls.Add(label6);
            Controls.Add(textboxncxzjd2);
            Controls.Add(label3);
            Controls.Add(textboxncxzjcfd2);
            Controls.Add(label4);
            Controls.Add(textboxncxzjd1);
            Controls.Add(label2);
            Controls.Add(datagridviewncxz);
            Controls.Add(checkboxncxztq);
            Controls.Add(textboxncxzjcfd1);
            Controls.Add(label1);
            Controls.Add(comboboxncxuanzhuan);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxncxuanzhuan);
            FormBorderStyle = FormBorderStyle.None;
            Name = "ncxuanzhuan";
            Text = "ncxuanzhuan";
            Load += ncxuanzhuan_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewncxz).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewncxz;
        public Sunny.UI.UITextBox textboxncxzjd4;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxncxzjcfd4;
        public AntdUI.Label label9;
        public Sunny.UI.UITextBox textboxncxzjd3;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxncxzjcfd3;
        public AntdUI.Label label6;
        public Sunny.UI.UITextBox textboxncxzjd2;
        public AntdUI.Label label3;
        public Sunny.UI.UITextBox textboxncxzjcfd2;
        public AntdUI.Label label4;
        public Sunny.UI.UITextBox textboxncxzjd1;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxncxztq;
        public Sunny.UI.UITextBox textboxncxzjcfd1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxncxuanzhuan;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxncxuanzhuan;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonncxzcz;
        public AntdUI.Label label19;
    }
}