﻿namespace ANYE_Balls
{
    partial class ncsanjiao1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewncsj1 = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxncsj1jt = new AntdUI.Checkbox();
            checkboxncsj1tq = new AntdUI.Checkbox();
            textboxncsj1jd = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            textboxncsj1jcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxncsanjiao1 = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxncsanjiao1 = new AntdUI.Checkbox();
            buttonncsjcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewncsj1).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewncsj1
            // 
            datagridviewncsj1.AllowUserToAddRows = false;
            datagridviewncsj1.AllowUserToDeleteRows = false;
            datagridviewncsj1.AllowUserToResizeColumns = false;
            datagridviewncsj1.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewncsj1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewncsj1.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewncsj1.BorderStyle = BorderStyle.Fixed3D;
            datagridviewncsj1.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewncsj1.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewncsj1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewncsj1.ColumnHeadersHeight = 40;
            datagridviewncsj1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewncsj1.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewncsj1.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewncsj1.EnableHeadersVisualStyles = false;
            datagridviewncsj1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewncsj1.GridColor = Color.AliceBlue;
            datagridviewncsj1.Location = new Point(309, 184);
            datagridviewncsj1.Name = "datagridviewncsj1";
            datagridviewncsj1.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewncsj1.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewncsj1.RowHeadersVisible = false;
            datagridviewncsj1.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewncsj1.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewncsj1.RowTemplate.Height = 25;
            datagridviewncsj1.ScrollBars = ScrollBars.None;
            datagridviewncsj1.SelectedIndex = -1;
            datagridviewncsj1.Size = new Size(294, 300);
            datagridviewncsj1.TabIndex = 47;
            datagridviewncsj1.CellValidating += datagridviewncsj1_CellValidating;
            datagridviewncsj1.CellValueChanged += datagridviewncsj1_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxncsj1jt
            // 
            checkboxncsj1jt.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsj1jt.Location = new Point(607, 215);
            checkboxncsj1jt.Name = "checkboxncsj1jt";
            checkboxncsj1jt.Size = new Size(167, 23);
            checkboxncsj1jt.TabIndex = 46;
            checkboxncsj1jt.Text = "箭头方向为准";
            checkboxncsj1jt.CheckedChanged += checkboxncsj1jt_CheckedChanged;
            // 
            // checkboxncsj1tq
            // 
            checkboxncsj1tq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsj1tq.Location = new Point(607, 186);
            checkboxncsj1tq.Name = "checkboxncsj1tq";
            checkboxncsj1tq.Size = new Size(131, 23);
            checkboxncsj1tq.TabIndex = 45;
            checkboxncsj1tq.Text = "自动吐球";
            checkboxncsj1tq.CheckedChanged += checkboxncsj1tq_CheckedChanged;
            // 
            // textboxncsj1jd
            // 
            textboxncsj1jd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncsj1jd.Location = new Point(125, 265);
            textboxncsj1jd.Margin = new Padding(4, 5, 4, 5);
            textboxncsj1jd.MinimumSize = new Size(1, 16);
            textboxncsj1jd.Name = "textboxncsj1jd";
            textboxncsj1jd.Padding = new Padding(5);
            textboxncsj1jd.RectColor = Color.FromArgb(255, 255, 192);
            textboxncsj1jd.ShowText = false;
            textboxncsj1jd.Size = new Size(70, 30);
            textboxncsj1jd.TabIndex = 44;
            textboxncsj1jd.Text = "0";
            textboxncsj1jd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncsj1jd.Watermark = "";
            textboxncsj1jd.TextChanged += textboxncsj1jd_TextChanged;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(71, 269);
            label2.Name = "label2";
            label2.Size = new Size(57, 23);
            label2.TabIndex = 43;
            label2.Text = "角度";
            // 
            // textboxncsj1jcfd
            // 
            textboxncsj1jcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncsj1jcfd.Location = new Point(125, 231);
            textboxncsj1jcfd.Margin = new Padding(4, 5, 4, 5);
            textboxncsj1jcfd.MinimumSize = new Size(1, 16);
            textboxncsj1jcfd.Name = "textboxncsj1jcfd";
            textboxncsj1jcfd.Padding = new Padding(5);
            textboxncsj1jcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxncsj1jcfd.ShowText = false;
            textboxncsj1jcfd.Size = new Size(70, 30);
            textboxncsj1jcfd.TabIndex = 42;
            textboxncsj1jcfd.Text = "0";
            textboxncsj1jcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncsj1jcfd.Watermark = "";
            textboxncsj1jcfd.TextChanged += textboxncsj1jcfd_TextChanged;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 41;
            label1.Text = "交叉幅度";
            // 
            // comboboxncsanjiao1
            // 
            comboboxncsanjiao1.DataSource = null;
            comboboxncsanjiao1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxncsanjiao1.DropDownWidth = 70;
            comboboxncsanjiao1.FillColor = Color.White;
            comboboxncsanjiao1.FillColorGradient = true;
            comboboxncsanjiao1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxncsanjiao1.ItemHeight = 40;
            comboboxncsanjiao1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxncsanjiao1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxncsanjiao1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxncsanjiao1.Location = new Point(125, 180);
            comboboxncsanjiao1.Margin = new Padding(0);
            comboboxncsanjiao1.MaxDropDownItems = 30;
            comboboxncsanjiao1.MinimumSize = new Size(63, 0);
            comboboxncsanjiao1.Name = "comboboxncsanjiao1";
            comboboxncsanjiao1.Padding = new Padding(0, 0, 30, 10);
            comboboxncsanjiao1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxncsanjiao1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxncsanjiao1.ScrollBarStyleInherited = false;
            comboboxncsanjiao1.Size = new Size(70, 31);
            comboboxncsanjiao1.SymbolSize = 24;
            comboboxncsanjiao1.TabIndex = 40;
            comboboxncsanjiao1.Text = "A";
            comboboxncsanjiao1.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxncsanjiao1.Watermark = "";
            comboboxncsanjiao1.SelectedIndexChanged += comboboxncsanjiao1_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 37;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 39;
            label7.Text = "快捷键";
            // 
            // checkboxncsanjiao1
            // 
            checkboxncsanjiao1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsanjiao1.Location = new Point(208, 184);
            checkboxncsanjiao1.Name = "checkboxncsanjiao1";
            checkboxncsanjiao1.Size = new Size(95, 23);
            checkboxncsanjiao1.TabIndex = 38;
            checkboxncsanjiao1.Text = "启用";
            checkboxncsanjiao1.CheckedChanged += checkboxncsanjiao1_CheckedChanged;
            // 
            // buttonncsjcz
            // 
            buttonncsjcz.BackHover = Color.Aquamarine;
            buttonncsjcz.DefaultBack = Color.Azure;
            buttonncsjcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonncsjcz.Location = new Point(208, 231);
            buttonncsjcz.Name = "buttonncsjcz";
            buttonncsjcz.Size = new Size(95, 64);
            buttonncsjcz.TabIndex = 145;
            buttonncsjcz.Text = "重置";
            buttonncsjcz.Click += buttonncsjcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 55;
            label19.Text = "鼠标方向：鼠标放在哪里往哪里合\r\n箭头方向：遥杆指向哪里往哪里合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // ncsanjiao1
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonncsjcz);
            Controls.Add(datagridviewncsj1);
            Controls.Add(checkboxncsj1jt);
            Controls.Add(checkboxncsj1tq);
            Controls.Add(textboxncsj1jd);
            Controls.Add(label2);
            Controls.Add(textboxncsj1jcfd);
            Controls.Add(label1);
            Controls.Add(comboboxncsanjiao1);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxncsanjiao1);
            FormBorderStyle = FormBorderStyle.None;
            Name = "ncsanjiao1";
            Text = "ncsanjiao1";
            Load += ncsanjiao1_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewncsj1).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewncsj1;
        public AntdUI.Checkbox checkboxncsj1jt;
        public AntdUI.Checkbox checkboxncsj1tq;
        public Sunny.UI.UITextBox textboxncsj1jd;
        public AntdUI.Label label2;
        public Sunny.UI.UITextBox textboxncsj1jcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxncsanjiao1;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxncsanjiao1;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonncsjcz;
        public AntdUI.Label label19;
    }
}