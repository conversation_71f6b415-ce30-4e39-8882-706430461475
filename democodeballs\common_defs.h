#ifndef COMMON_DEFS_H
#define COMMON_DEFS_H

#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <math.h>
#include <windows.h>

// 常量定义
#define PI 3.14159265358979

// 鼠标事件常量
#define MOUSEEVENTF_LEFTDOWN 0x0002  // 鼠标左键按下
#define MOUSEEVENTF_LEFTUP   0x0004  // 鼠标左键释放
#define KEYEVENTF_KEYUP      0x0002  // 键盘按键释放

// 虚拟键码常量
#define VK_LBUTTON           0x01    // 鼠标左键的虚拟键码

// 结构体定义
typedef struct {
    int x;
    int y;
} Point;

typedef struct {
    int left;
    int top;
    int right;
    int bottom;
} Rect;

// 合球参数结构体
typedef struct {
    int splitKey;           // 分身键的虚拟键码
    int pullKey;            // 吸球键的虚拟键码
    int angle;              // 合球角度
    int distance;           // 合球距离
    int delay1;             // 第一次延迟
    int delay2;             // 第二次延迟
    int delay3;             // 第三次延迟
    int delay4;             // 第四次延迟
    int delay5;             // 第五次延迟
    int delay6;             // 第六次延迟
    int delay7;             // 第七次延迟
    bool usePull;           // 是否使用吸球
    float joystickSize;     // 摇杆大小系数
} MergeParams;

// 方向向量结构体
typedef struct {
    double x;
    double y;
    double k;               // 斜率
    double magnitude;       // 向量长度
} Vector;

#endif // COMMON_DEFS_H 