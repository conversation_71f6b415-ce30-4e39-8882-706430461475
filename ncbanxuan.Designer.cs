﻿namespace ANYE_Balls
{
    partial class ncbanxuan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxncbxjd4 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            textboxncbxjcfd4 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            textboxncbxjd3 = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxncbxjcfd3 = new Sunny.UI.UITextBox();
            label6 = new AntdUI.Label();
            textboxncbxjd2 = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            textboxncbxjcfd2 = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxncbxjd1 = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            datagridviewncbx = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxncbxtq = new AntdUI.Checkbox();
            textboxncbxjcfd1 = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxncbanxuan = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxncbanxuan = new AntdUI.Checkbox();
            buttonncbxcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewncbx).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxncbxjd4
            // 
            textboxncbxjd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjd4.Location = new Point(125, 469);
            textboxncbxjd4.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjd4.MinimumSize = new Size(1, 16);
            textboxncbxjd4.Name = "textboxncbxjd4";
            textboxncbxjd4.Padding = new Padding(5);
            textboxncbxjd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjd4.ShowText = false;
            textboxncbxjd4.Size = new Size(70, 30);
            textboxncbxjd4.TabIndex = 120;
            textboxncbxjd4.Text = "0";
            textboxncbxjd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjd4.Watermark = "";
            textboxncbxjd4.TextChanged += textboxncbxjd4_TextChanged;
            textboxncbxjd4.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(60, 473);
            label8.Name = "label8";
            label8.Size = new Size(68, 23);
            label8.TabIndex = 119;
            label8.Text = "角度4";
            // 
            // textboxncbxjcfd4
            // 
            textboxncbxjcfd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjcfd4.Location = new Point(125, 435);
            textboxncbxjcfd4.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjcfd4.MinimumSize = new Size(1, 16);
            textboxncbxjcfd4.Name = "textboxncbxjcfd4";
            textboxncbxjcfd4.Padding = new Padding(5);
            textboxncbxjcfd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjcfd4.ShowText = false;
            textboxncbxjcfd4.Size = new Size(70, 30);
            textboxncbxjcfd4.TabIndex = 118;
            textboxncbxjcfd4.Text = "0";
            textboxncbxjcfd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjcfd4.Watermark = "";
            textboxncbxjcfd4.TextChanged += textboxncbxjcfd4_TextChanged;
            textboxncbxjcfd4.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 439);
            label9.Name = "label9";
            label9.Size = new Size(109, 23);
            label9.TabIndex = 117;
            label9.Text = "交叉幅度4";
            // 
            // textboxncbxjd3
            // 
            textboxncbxjd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjd3.Location = new Point(125, 401);
            textboxncbxjd3.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjd3.MinimumSize = new Size(1, 16);
            textboxncbxjd3.Name = "textboxncbxjd3";
            textboxncbxjd3.Padding = new Padding(5);
            textboxncbxjd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjd3.ShowText = false;
            textboxncbxjd3.Size = new Size(70, 30);
            textboxncbxjd3.TabIndex = 116;
            textboxncbxjd3.Text = "0";
            textboxncbxjd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjd3.Watermark = "";
            textboxncbxjd3.TextChanged += textboxncbxjd3_TextChanged;
            textboxncbxjd3.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(60, 405);
            label5.Name = "label5";
            label5.Size = new Size(68, 23);
            label5.TabIndex = 115;
            label5.Text = "角度3";
            // 
            // textboxncbxjcfd3
            // 
            textboxncbxjcfd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjcfd3.Location = new Point(125, 367);
            textboxncbxjcfd3.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjcfd3.MinimumSize = new Size(1, 16);
            textboxncbxjcfd3.Name = "textboxncbxjcfd3";
            textboxncbxjcfd3.Padding = new Padding(5);
            textboxncbxjcfd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjcfd3.ShowText = false;
            textboxncbxjcfd3.Size = new Size(70, 30);
            textboxncbxjcfd3.TabIndex = 114;
            textboxncbxjcfd3.Text = "0";
            textboxncbxjcfd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjcfd3.Watermark = "";
            textboxncbxjcfd3.TextChanged += textboxncbxjcfd3_TextChanged;
            textboxncbxjcfd3.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(19, 371);
            label6.Name = "label6";
            label6.Size = new Size(109, 23);
            label6.TabIndex = 113;
            label6.Text = "交叉幅度3";
            // 
            // textboxncbxjd2
            // 
            textboxncbxjd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjd2.Location = new Point(125, 333);
            textboxncbxjd2.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjd2.MinimumSize = new Size(1, 16);
            textboxncbxjd2.Name = "textboxncbxjd2";
            textboxncbxjd2.Padding = new Padding(5);
            textboxncbxjd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjd2.ShowText = false;
            textboxncbxjd2.Size = new Size(70, 30);
            textboxncbxjd2.TabIndex = 112;
            textboxncbxjd2.Text = "0";
            textboxncbxjd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjd2.Watermark = "";
            textboxncbxjd2.TextChanged += textboxncbxjd2_TextChanged;
            textboxncbxjd2.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(60, 337);
            label3.Name = "label3";
            label3.Size = new Size(68, 23);
            label3.TabIndex = 111;
            label3.Text = "角度2";
            // 
            // textboxncbxjcfd2
            // 
            textboxncbxjcfd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjcfd2.Location = new Point(125, 299);
            textboxncbxjcfd2.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjcfd2.MinimumSize = new Size(1, 16);
            textboxncbxjcfd2.Name = "textboxncbxjcfd2";
            textboxncbxjcfd2.Padding = new Padding(5);
            textboxncbxjcfd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjcfd2.ShowText = false;
            textboxncbxjcfd2.Size = new Size(70, 30);
            textboxncbxjcfd2.TabIndex = 110;
            textboxncbxjcfd2.Text = "0";
            textboxncbxjcfd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjcfd2.Watermark = "";
            textboxncbxjcfd2.TextChanged += textboxncbxjcfd2_TextChanged;
            textboxncbxjcfd2.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 303);
            label4.Name = "label4";
            label4.Size = new Size(109, 23);
            label4.TabIndex = 109;
            label4.Text = "交叉幅度2";
            // 
            // textboxncbxjd1
            // 
            textboxncbxjd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjd1.Location = new Point(125, 265);
            textboxncbxjd1.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjd1.MinimumSize = new Size(1, 16);
            textboxncbxjd1.Name = "textboxncbxjd1";
            textboxncbxjd1.Padding = new Padding(5);
            textboxncbxjd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjd1.ShowText = false;
            textboxncbxjd1.Size = new Size(70, 30);
            textboxncbxjd1.TabIndex = 108;
            textboxncbxjd1.Text = "0";
            textboxncbxjd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjd1.Watermark = "";
            textboxncbxjd1.TextChanged += textboxncbxjd1_TextChanged;
            textboxncbxjd1.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(60, 269);
            label2.Name = "label2";
            label2.Size = new Size(68, 23);
            label2.TabIndex = 107;
            label2.Text = "角度1";
            // 
            // datagridviewncbx
            // 
            datagridviewncbx.AllowUserToAddRows = false;
            datagridviewncbx.AllowUserToDeleteRows = false;
            datagridviewncbx.AllowUserToResizeColumns = false;
            datagridviewncbx.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewncbx.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewncbx.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewncbx.BorderStyle = BorderStyle.Fixed3D;
            datagridviewncbx.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewncbx.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewncbx.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewncbx.ColumnHeadersHeight = 40;
            datagridviewncbx.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewncbx.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewncbx.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewncbx.EnableHeadersVisualStyles = false;
            datagridviewncbx.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewncbx.GridColor = Color.AliceBlue;
            datagridviewncbx.Location = new Point(309, 184);
            datagridviewncbx.Name = "datagridviewncbx";
            datagridviewncbx.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewncbx.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewncbx.RowHeadersVisible = false;
            datagridviewncbx.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewncbx.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewncbx.RowTemplate.Height = 25;
            datagridviewncbx.ScrollBars = ScrollBars.None;
            datagridviewncbx.SelectedIndex = -1;
            datagridviewncbx.Size = new Size(294, 322);
            datagridviewncbx.TabIndex = 106;
            datagridviewncbx.CellValidating += datagridviewncbx_CellValidating;
            datagridviewncbx.CellValueChanged += datagridviewncbx_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxncbxtq
            // 
            checkboxncbxtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncbxtq.Location = new Point(607, 186);
            checkboxncbxtq.Name = "checkboxncbxtq";
            checkboxncbxtq.Size = new Size(131, 23);
            checkboxncbxtq.TabIndex = 105;
            checkboxncbxtq.Text = "自动吐球";
            checkboxncbxtq.CheckedChanged += checkboxncbxtq_CheckedChanged;
            // 
            // textboxncbxjcfd1
            // 
            textboxncbxjcfd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncbxjcfd1.Location = new Point(125, 231);
            textboxncbxjcfd1.Margin = new Padding(4, 5, 4, 5);
            textboxncbxjcfd1.MinimumSize = new Size(1, 16);
            textboxncbxjcfd1.Name = "textboxncbxjcfd1";
            textboxncbxjcfd1.Padding = new Padding(5);
            textboxncbxjcfd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxncbxjcfd1.ShowText = false;
            textboxncbxjcfd1.Size = new Size(70, 30);
            textboxncbxjcfd1.TabIndex = 104;
            textboxncbxjcfd1.Text = "0";
            textboxncbxjcfd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncbxjcfd1.Watermark = "";
            textboxncbxjcfd1.TextChanged += textboxncbxjcfd1_TextChanged;
            textboxncbxjcfd1.KeyPress += textboxncbxjcfd4_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(19, 235);
            label1.Name = "label1";
            label1.Size = new Size(109, 23);
            label1.TabIndex = 103;
            label1.Text = "交叉幅度1";
            // 
            // comboboxncbanxuan
            // 
            comboboxncbanxuan.DataSource = null;
            comboboxncbanxuan.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxncbanxuan.DropDownWidth = 70;
            comboboxncbanxuan.FillColor = Color.White;
            comboboxncbanxuan.FillColorGradient = true;
            comboboxncbanxuan.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxncbanxuan.ItemHeight = 40;
            comboboxncbanxuan.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxncbanxuan.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxncbanxuan.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxncbanxuan.Location = new Point(125, 180);
            comboboxncbanxuan.Margin = new Padding(0);
            comboboxncbanxuan.MaxDropDownItems = 30;
            comboboxncbanxuan.MinimumSize = new Size(63, 0);
            comboboxncbanxuan.Name = "comboboxncbanxuan";
            comboboxncbanxuan.Padding = new Padding(0, 0, 30, 10);
            comboboxncbanxuan.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxncbanxuan.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxncbanxuan.ScrollBarStyleInherited = false;
            comboboxncbanxuan.Size = new Size(70, 31);
            comboboxncbanxuan.SymbolSize = 24;
            comboboxncbanxuan.TabIndex = 102;
            comboboxncbanxuan.Text = "A";
            comboboxncbanxuan.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxncbanxuan.Watermark = "";
            comboboxncbanxuan.SelectedIndexChanged += comboboxncbanxuan_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 99;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 101;
            label7.Text = "快捷键";
            // 
            // checkboxncbanxuan
            // 
            checkboxncbanxuan.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncbanxuan.Location = new Point(208, 184);
            checkboxncbanxuan.Name = "checkboxncbanxuan";
            checkboxncbanxuan.Size = new Size(95, 23);
            checkboxncbanxuan.TabIndex = 100;
            checkboxncbanxuan.Text = "启用";
            checkboxncbanxuan.CheckedChanged += checkboxncbanxuan_CheckedChanged;
            // 
            // buttonncbxcz
            // 
            buttonncbxcz.BackHover = Color.Aquamarine;
            buttonncbxcz.DefaultBack = Color.Azure;
            buttonncbxcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonncbxcz.Location = new Point(208, 231);
            buttonncbxcz.Name = "buttonncbxcz";
            buttonncbxcz.Size = new Size(95, 64);
            buttonncbxcz.TabIndex = 146;
            buttonncbxcz.Text = "重置";
            buttonncbxcz.Click += buttonncbxcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 54;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标相对于遥杆哪边就往哪边旋";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // ncbanxuan
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonncbxcz);
            Controls.Add(textboxncbxjd4);
            Controls.Add(label8);
            Controls.Add(textboxncbxjcfd4);
            Controls.Add(label9);
            Controls.Add(textboxncbxjd3);
            Controls.Add(label5);
            Controls.Add(textboxncbxjcfd3);
            Controls.Add(label6);
            Controls.Add(textboxncbxjd2);
            Controls.Add(label3);
            Controls.Add(textboxncbxjcfd2);
            Controls.Add(label4);
            Controls.Add(textboxncbxjd1);
            Controls.Add(label2);
            Controls.Add(datagridviewncbx);
            Controls.Add(checkboxncbxtq);
            Controls.Add(textboxncbxjcfd1);
            Controls.Add(label1);
            Controls.Add(comboboxncbanxuan);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxncbanxuan);
            FormBorderStyle = FormBorderStyle.None;
            Name = "ncbanxuan";
            Text = "ncbanxuan";
            Load += ncbanxuan_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewncbx).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewncbx;
        public Sunny.UI.UITextBox textboxncbxjd4;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxncbxjcfd4;
        public AntdUI.Label label9;
        public Sunny.UI.UITextBox textboxncbxjd3;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxncbxjcfd3;
        public AntdUI.Label label6;
        public Sunny.UI.UITextBox textboxncbxjd2;
        public AntdUI.Label label3;
        public Sunny.UI.UITextBox textboxncbxjcfd2;
        public AntdUI.Label label4;
        public Sunny.UI.UITextBox textboxncbxjd1;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxncbxtq;
        public Sunny.UI.UITextBox textboxncbxjcfd1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxncbanxuan;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxncbanxuan;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonncbxcz;
        public AntdUI.Label label19;
    }
}