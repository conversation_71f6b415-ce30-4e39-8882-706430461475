﻿namespace ANYE_Balls
{
    partial class houyangcehe
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewhych = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxhychtq = new AntdUI.Checkbox();
            textboxhychjcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxhouyangcehe = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxhouyangcehe = new AntdUI.Checkbox();
            textboxhychjd = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            buttonnchychcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewhych).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewhych
            // 
            datagridviewhych.AllowUserToAddRows = false;
            datagridviewhych.AllowUserToDeleteRows = false;
            datagridviewhych.AllowUserToResizeColumns = false;
            datagridviewhych.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewhych.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewhych.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewhych.BorderStyle = BorderStyle.Fixed3D;
            datagridviewhych.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewhych.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewhych.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewhych.ColumnHeadersHeight = 40;
            datagridviewhych.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewhych.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewhych.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewhych.EnableHeadersVisualStyles = false;
            datagridviewhych.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewhych.GridColor = Color.AliceBlue;
            datagridviewhych.Location = new Point(309, 184);
            datagridviewhych.Name = "datagridviewhych";
            datagridviewhych.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewhych.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewhych.RowHeadersVisible = false;
            datagridviewhych.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewhych.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewhych.RowTemplate.Height = 25;
            datagridviewhych.ScrollBars = ScrollBars.None;
            datagridviewhych.SelectedIndex = -1;
            datagridviewhych.Size = new Size(294, 300);
            datagridviewhych.TabIndex = 74;
            datagridviewhych.CellValidating += datagridviewhych_CellValidating;
            datagridviewhych.CellValueChanged += datagridviewhych_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxhychtq
            // 
            checkboxhychtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxhychtq.Location = new Point(607, 186);
            checkboxhychtq.Name = "checkboxhychtq";
            checkboxhychtq.Size = new Size(131, 23);
            checkboxhychtq.TabIndex = 73;
            checkboxhychtq.Text = "自动吐球";
            checkboxhychtq.CheckedChanged += checkboxhychtq_CheckedChanged;
            // 
            // textboxhychjcfd
            // 
            textboxhychjcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxhychjcfd.Location = new Point(125, 231);
            textboxhychjcfd.Margin = new Padding(4, 5, 4, 5);
            textboxhychjcfd.MinimumSize = new Size(1, 16);
            textboxhychjcfd.Name = "textboxhychjcfd";
            textboxhychjcfd.Padding = new Padding(5);
            textboxhychjcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxhychjcfd.ShowText = false;
            textboxhychjcfd.Size = new Size(70, 30);
            textboxhychjcfd.TabIndex = 72;
            textboxhychjcfd.Text = "0";
            textboxhychjcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxhychjcfd.Watermark = "";
            textboxhychjcfd.TextChanged += textboxhychjcfd_TextChanged;
            textboxhychjcfd.KeyPress += textboxhychjcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 71;
            label1.Text = "交叉幅度";
            // 
            // comboboxhouyangcehe
            // 
            comboboxhouyangcehe.DataSource = null;
            comboboxhouyangcehe.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxhouyangcehe.DropDownWidth = 200;
            comboboxhouyangcehe.FillColor = Color.White;
            comboboxhouyangcehe.FillColorGradient = true;
            comboboxhouyangcehe.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxhouyangcehe.ItemHeight = 40;
            comboboxhouyangcehe.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxhouyangcehe.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxhouyangcehe.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxhouyangcehe.Location = new Point(125, 180);
            comboboxhouyangcehe.Margin = new Padding(0);
            comboboxhouyangcehe.MinimumSize = new Size(63, 0);
            comboboxhouyangcehe.Name = "comboboxhouyangcehe";
            comboboxhouyangcehe.Padding = new Padding(0, 0, 30, 10);
            comboboxhouyangcehe.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxhouyangcehe.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxhouyangcehe.ScrollBarStyleInherited = false;
            comboboxhouyangcehe.Size = new Size(70, 31);
            comboboxhouyangcehe.SymbolSize = 24;
            comboboxhouyangcehe.TabIndex = 70;
            comboboxhouyangcehe.Text = "A";
            comboboxhouyangcehe.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxhouyangcehe.Watermark = "";
            comboboxhouyangcehe.SelectedIndexChanged += comboboxhouyangcehe_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 67;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 69;
            label7.Text = "快捷键";
            // 
            // checkboxhouyangcehe
            // 
            checkboxhouyangcehe.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxhouyangcehe.Location = new Point(208, 184);
            checkboxhouyangcehe.Name = "checkboxhouyangcehe";
            checkboxhouyangcehe.Size = new Size(95, 23);
            checkboxhouyangcehe.TabIndex = 68;
            checkboxhouyangcehe.Text = "启用";
            checkboxhouyangcehe.CheckedChanged += checkboxhouyangcehe_CheckedChanged;
            // 
            // textboxhychjd
            // 
            textboxhychjd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxhychjd.Location = new Point(125, 265);
            textboxhychjd.Margin = new Padding(4, 5, 4, 5);
            textboxhychjd.MinimumSize = new Size(1, 16);
            textboxhychjd.Name = "textboxhychjd";
            textboxhychjd.Padding = new Padding(5);
            textboxhychjd.RectColor = Color.FromArgb(255, 255, 192);
            textboxhychjd.ShowText = false;
            textboxhychjd.Size = new Size(70, 30);
            textboxhychjd.TabIndex = 76;
            textboxhychjd.Text = "0";
            textboxhychjd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxhychjd.Watermark = "";
            textboxhychjd.TextChanged += textboxhychjd_TextChanged;
            textboxhychjd.KeyPress += textboxhychjd_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(28, 269);
            label2.Name = "label2";
            label2.Size = new Size(100, 23);
            label2.TabIndex = 75;
            label2.Text = "后仰角度";
            // 
            // buttonnchychcz
            // 
            buttonnchychcz.BackHover = Color.Aquamarine;
            buttonnchychcz.DefaultBack = Color.Azure;
            buttonnchychcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonnchychcz.Location = new Point(208, 231);
            buttonnchychcz.Name = "buttonnchychcz";
            buttonnchychcz.Size = new Size(95, 64);
            buttonnchychcz.TabIndex = 147;
            buttonnchychcz.Text = "重置";
            buttonnchychcz.Click += buttonnchychcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 54;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标放在哪个位置就往哪个位置合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // houyangcehe
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonnchychcz);
            Controls.Add(textboxhychjd);
            Controls.Add(label2);
            Controls.Add(datagridviewhych);
            Controls.Add(checkboxhychtq);
            Controls.Add(textboxhychjcfd);
            Controls.Add(label1);
            Controls.Add(comboboxhouyangcehe);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxhouyangcehe);
            FormBorderStyle = FormBorderStyle.None;
            Name = "houyangcehe";
            Text = "houyangcehe";
            Load += houyangcehe_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewhych).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewhych;
        public AntdUI.Checkbox checkboxhychtq;
        public Sunny.UI.UITextBox textboxhychjcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxhouyangcehe;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxhouyangcehe;
        public Sunny.UI.UITextBox textboxhychjd;
        public AntdUI.Label label2;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonnchychcz;
        public AntdUI.Label label19;
    }
}