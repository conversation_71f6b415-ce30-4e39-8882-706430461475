﻿namespace ANYE_Balls
{
    partial class sanjiao2
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewsj2 = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxsj2jt = new AntdUI.Checkbox();
            checkboxsj2tq = new AntdUI.Checkbox();
            textboxsj2jd = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            textboxsj2jcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxsanjiao2 = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxsanjiao2 = new AntdUI.Checkbox();
            buttonsj2cz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewsj2).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewsj2
            // 
            datagridviewsj2.AllowUserToAddRows = false;
            datagridviewsj2.AllowUserToDeleteRows = false;
            datagridviewsj2.AllowUserToResizeColumns = false;
            datagridviewsj2.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewsj2.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewsj2.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewsj2.BorderStyle = BorderStyle.Fixed3D;
            datagridviewsj2.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewsj2.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewsj2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewsj2.ColumnHeadersHeight = 40;
            datagridviewsj2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewsj2.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewsj2.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewsj2.EnableHeadersVisualStyles = false;
            datagridviewsj2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewsj2.GridColor = Color.AliceBlue;
            datagridviewsj2.Location = new Point(309, 184);
            datagridviewsj2.Name = "datagridviewsj2";
            datagridviewsj2.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewsj2.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewsj2.RowHeadersVisible = false;
            datagridviewsj2.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewsj2.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewsj2.RowTemplate.Height = 25;
            datagridviewsj2.ScrollBars = ScrollBars.None;
            datagridviewsj2.SelectedIndex = -1;
            datagridviewsj2.Size = new Size(294, 300);
            datagridviewsj2.TabIndex = 47;
            datagridviewsj2.CellValidating += datagridviewsj2_CellValidating;
            datagridviewsj2.CellValueChanged += datagridviewsj2_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxsj2jt
            // 
            checkboxsj2jt.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsj2jt.Location = new Point(607, 215);
            checkboxsj2jt.Name = "checkboxsj2jt";
            checkboxsj2jt.Size = new Size(167, 23);
            checkboxsj2jt.TabIndex = 46;
            checkboxsj2jt.Text = "箭头方向为准";
            checkboxsj2jt.CheckedChanged += checkboxsj2jt_CheckedChanged;
            // 
            // checkboxsj2tq
            // 
            checkboxsj2tq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsj2tq.Location = new Point(607, 186);
            checkboxsj2tq.Name = "checkboxsj2tq";
            checkboxsj2tq.Size = new Size(131, 23);
            checkboxsj2tq.TabIndex = 45;
            checkboxsj2tq.Text = "自动吐球";
            checkboxsj2tq.CheckedChanged += checkboxsj2tq_CheckedChanged;
            // 
            // textboxsj2jd
            // 
            textboxsj2jd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxsj2jd.Location = new Point(125, 265);
            textboxsj2jd.Margin = new Padding(4, 5, 4, 5);
            textboxsj2jd.MinimumSize = new Size(1, 16);
            textboxsj2jd.Name = "textboxsj2jd";
            textboxsj2jd.Padding = new Padding(5);
            textboxsj2jd.RectColor = Color.FromArgb(255, 255, 192);
            textboxsj2jd.ShowText = false;
            textboxsj2jd.Size = new Size(70, 30);
            textboxsj2jd.TabIndex = 44;
            textboxsj2jd.Text = "0";
            textboxsj2jd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxsj2jd.Watermark = "";
            textboxsj2jd.TextChanged += textboxsj2jd_TextChanged;
            textboxsj2jd.KeyPress += textboxsj2jd_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(71, 269);
            label2.Name = "label2";
            label2.Size = new Size(57, 23);
            label2.TabIndex = 43;
            label2.Text = "角度";
            // 
            // textboxsj2jcfd
            // 
            textboxsj2jcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxsj2jcfd.Location = new Point(125, 231);
            textboxsj2jcfd.Margin = new Padding(4, 5, 4, 5);
            textboxsj2jcfd.MinimumSize = new Size(1, 16);
            textboxsj2jcfd.Name = "textboxsj2jcfd";
            textboxsj2jcfd.Padding = new Padding(5);
            textboxsj2jcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxsj2jcfd.ShowText = false;
            textboxsj2jcfd.Size = new Size(70, 30);
            textboxsj2jcfd.TabIndex = 42;
            textboxsj2jcfd.Text = "0";
            textboxsj2jcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxsj2jcfd.Watermark = "";
            textboxsj2jcfd.TextChanged += textboxsj2jcfd_TextChanged;
            textboxsj2jcfd.KeyPress += textboxsj2jcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 41;
            label1.Text = "交叉幅度";
            // 
            // comboboxsanjiao2
            // 
            comboboxsanjiao2.DataSource = null;
            comboboxsanjiao2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxsanjiao2.DropDownWidth = 70;
            comboboxsanjiao2.FillColor = Color.White;
            comboboxsanjiao2.FillColorGradient = true;
            comboboxsanjiao2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxsanjiao2.ItemHeight = 40;
            comboboxsanjiao2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxsanjiao2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxsanjiao2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxsanjiao2.Location = new Point(125, 180);
            comboboxsanjiao2.Margin = new Padding(0);
            comboboxsanjiao2.MaxDropDownItems = 30;
            comboboxsanjiao2.MinimumSize = new Size(63, 0);
            comboboxsanjiao2.Name = "comboboxsanjiao2";
            comboboxsanjiao2.Padding = new Padding(0, 0, 30, 10);
            comboboxsanjiao2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxsanjiao2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxsanjiao2.ScrollBarStyleInherited = false;
            comboboxsanjiao2.Size = new Size(70, 31);
            comboboxsanjiao2.SymbolSize = 24;
            comboboxsanjiao2.TabIndex = 40;
            comboboxsanjiao2.Text = "A";
            comboboxsanjiao2.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxsanjiao2.Watermark = "";
            comboboxsanjiao2.SelectedIndexChanged += comboboxsanjiao2_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 37;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 39;
            label7.Text = "快捷键";
            // 
            // checkboxsanjiao2
            // 
            checkboxsanjiao2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsanjiao2.Location = new Point(208, 184);
            checkboxsanjiao2.Name = "checkboxsanjiao2";
            checkboxsanjiao2.Size = new Size(95, 23);
            checkboxsanjiao2.TabIndex = 38;
            checkboxsanjiao2.Text = "启用";
            checkboxsanjiao2.CheckedChanged += checkboxsanjiao2_CheckedChanged;
            // 
            // buttonsj2cz
            // 
            buttonsj2cz.BackHover = Color.Aquamarine;
            buttonsj2cz.DefaultBack = Color.Azure;
            buttonsj2cz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonsj2cz.Location = new Point(208, 231);
            buttonsj2cz.Name = "buttonsj2cz";
            buttonsj2cz.Size = new Size(95, 64);
            buttonsj2cz.TabIndex = 48;
            buttonsj2cz.Text = "重置";
            buttonsj2cz.Click += buttonsj2cz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "鼠标方向：鼠标放在哪里往哪里合\r\n箭头方向：遥杆指向哪里往哪里合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // sanjiao2
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonsj2cz);
            Controls.Add(datagridviewsj2);
            Controls.Add(checkboxsj2jt);
            Controls.Add(checkboxsj2tq);
            Controls.Add(textboxsj2jd);
            Controls.Add(label2);
            Controls.Add(textboxsj2jcfd);
            Controls.Add(label1);
            Controls.Add(comboboxsanjiao2);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxsanjiao2);
            FormBorderStyle = FormBorderStyle.None;
            Name = "sanjiao2";
            Text = "sanjiao2";
            Load += sanjiao2_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewsj2).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewsj2;
        public AntdUI.Checkbox checkboxsj2jt;
        public AntdUI.Checkbox checkboxsj2tq;
        public Sunny.UI.UITextBox textboxsj2jd;
        public AntdUI.Label label2;
        public Sunny.UI.UITextBox textboxsj2jcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxsanjiao2;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxsanjiao2;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonsj2cz;
        public AntdUI.Label label19;
    }
}