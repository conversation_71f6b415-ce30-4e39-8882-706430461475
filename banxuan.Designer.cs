﻿namespace ANYE_Balls
{
    partial class banxuan
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxbxjd1 = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            datagridviewbx = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxbxtq = new AntdUI.Checkbox();
            textboxbxjcfd1 = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxbanxuan = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxbanxuan = new AntdUI.Checkbox();
            textboxbxjd2 = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            textboxbxjcfd2 = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxbxjd3 = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxbxjcfd3 = new Sunny.UI.UITextBox();
            label6 = new AntdUI.Label();
            textboxbxjd4 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            textboxbxjcfd4 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            buttonbxcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewbx).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxbxjd1
            // 
            textboxbxjd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjd1.Location = new Point(125, 265);
            textboxbxjd1.Margin = new Padding(4, 5, 4, 5);
            textboxbxjd1.MinimumSize = new Size(1, 16);
            textboxbxjd1.Name = "textboxbxjd1";
            textboxbxjd1.Padding = new Padding(5);
            textboxbxjd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjd1.ShowText = false;
            textboxbxjd1.Size = new Size(70, 30);
            textboxbxjd1.TabIndex = 86;
            textboxbxjd1.Text = "0";
            textboxbxjd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjd1.Watermark = "";
            textboxbxjd1.TextChanged += textboxbxjd1_TextChanged;
            textboxbxjd1.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(60, 269);
            label2.Name = "label2";
            label2.Size = new Size(68, 23);
            label2.TabIndex = 85;
            label2.Text = "角度1";
            // 
            // datagridviewbx
            // 
            datagridviewbx.AllowUserToAddRows = false;
            datagridviewbx.AllowUserToDeleteRows = false;
            datagridviewbx.AllowUserToResizeColumns = false;
            datagridviewbx.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewbx.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewbx.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewbx.BorderStyle = BorderStyle.Fixed3D;
            datagridviewbx.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewbx.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewbx.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewbx.ColumnHeadersHeight = 40;
            datagridviewbx.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewbx.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewbx.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewbx.EnableHeadersVisualStyles = false;
            datagridviewbx.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewbx.GridColor = Color.AliceBlue;
            datagridviewbx.Location = new Point(309, 184);
            datagridviewbx.Name = "datagridviewbx";
            datagridviewbx.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewbx.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewbx.RowHeadersVisible = false;
            datagridviewbx.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewbx.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewbx.RowTemplate.Height = 25;
            datagridviewbx.ScrollBars = ScrollBars.None;
            datagridviewbx.SelectedIndex = -1;
            datagridviewbx.Size = new Size(294, 322);
            datagridviewbx.TabIndex = 84;
            datagridviewbx.CellValidating += datagridviewbx_CellValidating;
            datagridviewbx.CellValueChanged += datagridviewbx_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxbxtq
            // 
            checkboxbxtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxbxtq.Location = new Point(607, 186);
            checkboxbxtq.Name = "checkboxbxtq";
            checkboxbxtq.Size = new Size(131, 23);
            checkboxbxtq.TabIndex = 83;
            checkboxbxtq.Text = "自动吐球";
            checkboxbxtq.CheckedChanged += checkboxbxtq_CheckedChanged;
            // 
            // textboxbxjcfd1
            // 
            textboxbxjcfd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjcfd1.Location = new Point(125, 231);
            textboxbxjcfd1.Margin = new Padding(4, 5, 4, 5);
            textboxbxjcfd1.MinimumSize = new Size(1, 16);
            textboxbxjcfd1.Name = "textboxbxjcfd1";
            textboxbxjcfd1.Padding = new Padding(5);
            textboxbxjcfd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjcfd1.ShowText = false;
            textboxbxjcfd1.Size = new Size(70, 30);
            textboxbxjcfd1.TabIndex = 82;
            textboxbxjcfd1.Text = "0";
            textboxbxjcfd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjcfd1.Watermark = "";
            textboxbxjcfd1.TextChanged += textboxbxjcfd1_TextChanged;
            textboxbxjcfd1.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(19, 235);
            label1.Name = "label1";
            label1.Size = new Size(109, 23);
            label1.TabIndex = 81;
            label1.Text = "交叉幅度1";
            // 
            // comboboxbanxuan
            // 
            comboboxbanxuan.DataSource = null;
            comboboxbanxuan.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxbanxuan.DropDownWidth = 200;
            comboboxbanxuan.FillColor = Color.White;
            comboboxbanxuan.FillColorGradient = true;
            comboboxbanxuan.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxbanxuan.ItemHeight = 40;
            comboboxbanxuan.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxbanxuan.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxbanxuan.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxbanxuan.Location = new Point(125, 180);
            comboboxbanxuan.Margin = new Padding(0);
            comboboxbanxuan.MinimumSize = new Size(63, 0);
            comboboxbanxuan.Name = "comboboxbanxuan";
            comboboxbanxuan.Padding = new Padding(0, 0, 30, 10);
            comboboxbanxuan.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxbanxuan.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxbanxuan.ScrollBarStyleInherited = false;
            comboboxbanxuan.Size = new Size(70, 31);
            comboboxbanxuan.SymbolSize = 24;
            comboboxbanxuan.TabIndex = 80;
            comboboxbanxuan.Text = "A";
            comboboxbanxuan.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxbanxuan.Watermark = "";
            comboboxbanxuan.SelectedIndexChanged += comboboxbanxuan_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 77;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 79;
            label7.Text = "快捷键";
            // 
            // checkboxbanxuan
            // 
            checkboxbanxuan.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxbanxuan.Location = new Point(208, 184);
            checkboxbanxuan.Name = "checkboxbanxuan";
            checkboxbanxuan.Size = new Size(95, 23);
            checkboxbanxuan.TabIndex = 78;
            checkboxbanxuan.Text = "启用";
            checkboxbanxuan.CheckedChanged += checkboxbanxuan_CheckedChanged;
            // 
            // textboxbxjd2
            // 
            textboxbxjd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjd2.Location = new Point(125, 333);
            textboxbxjd2.Margin = new Padding(4, 5, 4, 5);
            textboxbxjd2.MinimumSize = new Size(1, 16);
            textboxbxjd2.Name = "textboxbxjd2";
            textboxbxjd2.Padding = new Padding(5);
            textboxbxjd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjd2.ShowText = false;
            textboxbxjd2.Size = new Size(70, 30);
            textboxbxjd2.TabIndex = 90;
            textboxbxjd2.Text = "0";
            textboxbxjd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjd2.Watermark = "";
            textboxbxjd2.TextChanged += textboxbxjd2_TextChanged;
            textboxbxjd2.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(60, 337);
            label3.Name = "label3";
            label3.Size = new Size(68, 23);
            label3.TabIndex = 89;
            label3.Text = "角度2";
            // 
            // textboxbxjcfd2
            // 
            textboxbxjcfd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjcfd2.Location = new Point(125, 299);
            textboxbxjcfd2.Margin = new Padding(4, 5, 4, 5);
            textboxbxjcfd2.MinimumSize = new Size(1, 16);
            textboxbxjcfd2.Name = "textboxbxjcfd2";
            textboxbxjcfd2.Padding = new Padding(5);
            textboxbxjcfd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjcfd2.ShowText = false;
            textboxbxjcfd2.Size = new Size(70, 30);
            textboxbxjcfd2.TabIndex = 88;
            textboxbxjcfd2.Text = "0";
            textboxbxjcfd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjcfd2.Watermark = "";
            textboxbxjcfd2.TextChanged += textboxbxjcfd2_TextChanged;
            textboxbxjcfd2.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 303);
            label4.Name = "label4";
            label4.Size = new Size(109, 23);
            label4.TabIndex = 87;
            label4.Text = "交叉幅度2";
            // 
            // textboxbxjd3
            // 
            textboxbxjd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjd3.Location = new Point(125, 401);
            textboxbxjd3.Margin = new Padding(4, 5, 4, 5);
            textboxbxjd3.MinimumSize = new Size(1, 16);
            textboxbxjd3.Name = "textboxbxjd3";
            textboxbxjd3.Padding = new Padding(5);
            textboxbxjd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjd3.ShowText = false;
            textboxbxjd3.Size = new Size(70, 30);
            textboxbxjd3.TabIndex = 94;
            textboxbxjd3.Text = "0";
            textboxbxjd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjd3.Watermark = "";
            textboxbxjd3.TextChanged += textboxbxjd3_TextChanged;
            textboxbxjd3.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(60, 405);
            label5.Name = "label5";
            label5.Size = new Size(68, 23);
            label5.TabIndex = 93;
            label5.Text = "角度3";
            // 
            // textboxbxjcfd3
            // 
            textboxbxjcfd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjcfd3.Location = new Point(125, 367);
            textboxbxjcfd3.Margin = new Padding(4, 5, 4, 5);
            textboxbxjcfd3.MinimumSize = new Size(1, 16);
            textboxbxjcfd3.Name = "textboxbxjcfd3";
            textboxbxjcfd3.Padding = new Padding(5);
            textboxbxjcfd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjcfd3.ShowText = false;
            textboxbxjcfd3.Size = new Size(70, 30);
            textboxbxjcfd3.TabIndex = 92;
            textboxbxjcfd3.Text = "0";
            textboxbxjcfd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjcfd3.Watermark = "";
            textboxbxjcfd3.TextChanged += textboxbxjcfd3_TextChanged;
            textboxbxjcfd3.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(19, 371);
            label6.Name = "label6";
            label6.Size = new Size(109, 23);
            label6.TabIndex = 91;
            label6.Text = "交叉幅度3";
            // 
            // textboxbxjd4
            // 
            textboxbxjd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjd4.Location = new Point(125, 469);
            textboxbxjd4.Margin = new Padding(4, 5, 4, 5);
            textboxbxjd4.MinimumSize = new Size(1, 16);
            textboxbxjd4.Name = "textboxbxjd4";
            textboxbxjd4.Padding = new Padding(5);
            textboxbxjd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjd4.ShowText = false;
            textboxbxjd4.Size = new Size(70, 30);
            textboxbxjd4.TabIndex = 98;
            textboxbxjd4.Text = "0";
            textboxbxjd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjd4.Watermark = "";
            textboxbxjd4.TextChanged += textboxbxjd4_TextChanged;
            textboxbxjd4.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(60, 473);
            label8.Name = "label8";
            label8.Size = new Size(68, 23);
            label8.TabIndex = 97;
            label8.Text = "角度4";
            // 
            // textboxbxjcfd4
            // 
            textboxbxjcfd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbxjcfd4.Location = new Point(125, 435);
            textboxbxjcfd4.Margin = new Padding(4, 5, 4, 5);
            textboxbxjcfd4.MinimumSize = new Size(1, 16);
            textboxbxjcfd4.Name = "textboxbxjcfd4";
            textboxbxjcfd4.Padding = new Padding(5);
            textboxbxjcfd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxbxjcfd4.ShowText = false;
            textboxbxjcfd4.Size = new Size(70, 30);
            textboxbxjcfd4.TabIndex = 96;
            textboxbxjcfd4.Text = "0";
            textboxbxjcfd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbxjcfd4.Watermark = "";
            textboxbxjcfd4.TextChanged += textboxbxjcfd4_TextChanged;
            textboxbxjcfd4.KeyPress += textboxbxjcfd4_KeyPress;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 439);
            label9.Name = "label9";
            label9.Size = new Size(109, 23);
            label9.TabIndex = 95;
            label9.Text = "交叉幅度4";
            // 
            // buttonbxcz
            // 
            buttonbxcz.BackHover = Color.Aquamarine;
            buttonbxcz.DefaultBack = Color.Azure;
            buttonbxcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonbxcz.Location = new Point(208, 231);
            buttonbxcz.Name = "buttonbxcz";
            buttonbxcz.Size = new Size(95, 64);
            buttonbxcz.TabIndex = 144;
            buttonbxcz.Text = "重置";
            buttonbxcz.Click += buttonbxcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 53;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标相对于遥杆哪边就往哪边旋";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // banxuan
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonbxcz);
            Controls.Add(textboxbxjd4);
            Controls.Add(label8);
            Controls.Add(textboxbxjcfd4);
            Controls.Add(label9);
            Controls.Add(textboxbxjd3);
            Controls.Add(label5);
            Controls.Add(textboxbxjcfd3);
            Controls.Add(label6);
            Controls.Add(textboxbxjd2);
            Controls.Add(label3);
            Controls.Add(textboxbxjcfd2);
            Controls.Add(label4);
            Controls.Add(textboxbxjd1);
            Controls.Add(label2);
            Controls.Add(datagridviewbx);
            Controls.Add(checkboxbxtq);
            Controls.Add(textboxbxjcfd1);
            Controls.Add(label1);
            Controls.Add(comboboxbanxuan);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxbanxuan);
            FormBorderStyle = FormBorderStyle.None;
            Name = "banxuan";
            Text = "banxuan";
            Load += banxuan_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewbx).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewbx;
        public Sunny.UI.UITextBox textboxbxjd1;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxbxtq;
        public Sunny.UI.UITextBox textboxbxjcfd1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxbanxuan;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxbanxuan;
        public Sunny.UI.UITextBox textboxbxjd2;
        public AntdUI.Label label3;
        public Sunny.UI.UITextBox textboxbxjcfd2;
        public AntdUI.Label label4;
        public Sunny.UI.UITextBox textboxbxjd3;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxbxjcfd3;
        public AntdUI.Label label6;
        public Sunny.UI.UITextBox textboxbxjd4;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxbxjcfd4;
        public AntdUI.Label label9;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonbxcz;
        public AntdUI.Label label19;
    }
}