﻿namespace ANYE_Balls
{
    partial class sheshou
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxssjd4 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            textboxssjcfd4 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            textboxssjd3 = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxssjcfd3 = new Sunny.UI.UITextBox();
            label6 = new AntdUI.Label();
            textboxssjd2 = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            textboxssjcfd2 = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxssjd1 = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            datagridviewss = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxsstq = new AntdUI.Checkbox();
            textboxssjcfd1 = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxsheshou = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxsheshou = new AntdUI.Checkbox();
            buttonsscz = new AntdUI.Button();
            button1 = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewss).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxssjd4
            // 
            textboxssjd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjd4.Location = new Point(125, 469);
            textboxssjd4.Margin = new Padding(4, 5, 4, 5);
            textboxssjd4.MinimumSize = new Size(1, 16);
            textboxssjd4.Name = "textboxssjd4";
            textboxssjd4.Padding = new Padding(5);
            textboxssjd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjd4.ShowText = false;
            textboxssjd4.Size = new Size(70, 30);
            textboxssjd4.TabIndex = 142;
            textboxssjd4.Text = "0";
            textboxssjd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjd4.Watermark = "";
            textboxssjd4.TextChanged += textboxssjd4_TextChanged;
            textboxssjd4.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(60, 473);
            label8.Name = "label8";
            label8.Size = new Size(68, 23);
            label8.TabIndex = 141;
            label8.Text = "角度4";
            // 
            // textboxssjcfd4
            // 
            textboxssjcfd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjcfd4.Location = new Point(125, 435);
            textboxssjcfd4.Margin = new Padding(4, 5, 4, 5);
            textboxssjcfd4.MinimumSize = new Size(1, 16);
            textboxssjcfd4.Name = "textboxssjcfd4";
            textboxssjcfd4.Padding = new Padding(5);
            textboxssjcfd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjcfd4.ShowText = false;
            textboxssjcfd4.Size = new Size(70, 30);
            textboxssjcfd4.TabIndex = 140;
            textboxssjcfd4.Text = "0";
            textboxssjcfd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjcfd4.Watermark = "";
            textboxssjcfd4.TextChanged += textboxssjcfd4_TextChanged;
            textboxssjcfd4.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 439);
            label9.Name = "label9";
            label9.Size = new Size(109, 23);
            label9.TabIndex = 139;
            label9.Text = "交叉幅度4";
            // 
            // textboxssjd3
            // 
            textboxssjd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjd3.Location = new Point(125, 401);
            textboxssjd3.Margin = new Padding(4, 5, 4, 5);
            textboxssjd3.MinimumSize = new Size(1, 16);
            textboxssjd3.Name = "textboxssjd3";
            textboxssjd3.Padding = new Padding(5);
            textboxssjd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjd3.ShowText = false;
            textboxssjd3.Size = new Size(70, 30);
            textboxssjd3.TabIndex = 138;
            textboxssjd3.Text = "0";
            textboxssjd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjd3.Watermark = "";
            textboxssjd3.TextChanged += textboxssjd3_TextChanged;
            textboxssjd3.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(60, 405);
            label5.Name = "label5";
            label5.Size = new Size(68, 23);
            label5.TabIndex = 137;
            label5.Text = "角度3";
            // 
            // textboxssjcfd3
            // 
            textboxssjcfd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjcfd3.Location = new Point(125, 367);
            textboxssjcfd3.Margin = new Padding(4, 5, 4, 5);
            textboxssjcfd3.MinimumSize = new Size(1, 16);
            textboxssjcfd3.Name = "textboxssjcfd3";
            textboxssjcfd3.Padding = new Padding(5);
            textboxssjcfd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjcfd3.ShowText = false;
            textboxssjcfd3.Size = new Size(70, 30);
            textboxssjcfd3.TabIndex = 136;
            textboxssjcfd3.Text = "0";
            textboxssjcfd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjcfd3.Watermark = "";
            textboxssjcfd3.TextChanged += textboxssjcfd3_TextChanged;
            textboxssjcfd3.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(19, 371);
            label6.Name = "label6";
            label6.Size = new Size(109, 23);
            label6.TabIndex = 135;
            label6.Text = "交叉幅度3";
            // 
            // textboxssjd2
            // 
            textboxssjd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjd2.Location = new Point(125, 333);
            textboxssjd2.Margin = new Padding(4, 5, 4, 5);
            textboxssjd2.MinimumSize = new Size(1, 16);
            textboxssjd2.Name = "textboxssjd2";
            textboxssjd2.Padding = new Padding(5);
            textboxssjd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjd2.ShowText = false;
            textboxssjd2.Size = new Size(70, 30);
            textboxssjd2.TabIndex = 134;
            textboxssjd2.Text = "0";
            textboxssjd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjd2.Watermark = "";
            textboxssjd2.TextChanged += textboxssjd2_TextChanged;
            textboxssjd2.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(60, 337);
            label3.Name = "label3";
            label3.Size = new Size(68, 23);
            label3.TabIndex = 133;
            label3.Text = "角度2";
            // 
            // textboxssjcfd2
            // 
            textboxssjcfd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjcfd2.Location = new Point(125, 299);
            textboxssjcfd2.Margin = new Padding(4, 5, 4, 5);
            textboxssjcfd2.MinimumSize = new Size(1, 16);
            textboxssjcfd2.Name = "textboxssjcfd2";
            textboxssjcfd2.Padding = new Padding(5);
            textboxssjcfd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjcfd2.ShowText = false;
            textboxssjcfd2.Size = new Size(70, 30);
            textboxssjcfd2.TabIndex = 132;
            textboxssjcfd2.Text = "0";
            textboxssjcfd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjcfd2.Watermark = "";
            textboxssjcfd2.TextChanged += textboxssjcfd2_TextChanged;
            textboxssjcfd2.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 303);
            label4.Name = "label4";
            label4.Size = new Size(109, 23);
            label4.TabIndex = 131;
            label4.Text = "交叉幅度2";
            // 
            // textboxssjd1
            // 
            textboxssjd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjd1.Location = new Point(125, 265);
            textboxssjd1.Margin = new Padding(4, 5, 4, 5);
            textboxssjd1.MinimumSize = new Size(1, 16);
            textboxssjd1.Name = "textboxssjd1";
            textboxssjd1.Padding = new Padding(5);
            textboxssjd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjd1.ShowText = false;
            textboxssjd1.Size = new Size(70, 30);
            textboxssjd1.TabIndex = 130;
            textboxssjd1.Text = "0";
            textboxssjd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjd1.Watermark = "";
            textboxssjd1.TextChanged += textboxssjd1_TextChanged;
            textboxssjd1.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(60, 269);
            label2.Name = "label2";
            label2.Size = new Size(68, 23);
            label2.TabIndex = 129;
            label2.Text = "角度1";
            // 
            // datagridviewss
            // 
            datagridviewss.AllowUserToAddRows = false;
            datagridviewss.AllowUserToDeleteRows = false;
            datagridviewss.AllowUserToResizeColumns = false;
            datagridviewss.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewss.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewss.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewss.BorderStyle = BorderStyle.Fixed3D;
            datagridviewss.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewss.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewss.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewss.ColumnHeadersHeight = 40;
            datagridviewss.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewss.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewss.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewss.EnableHeadersVisualStyles = false;
            datagridviewss.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewss.GridColor = Color.AliceBlue;
            datagridviewss.Location = new Point(309, 184);
            datagridviewss.Name = "datagridviewss";
            datagridviewss.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewss.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewss.RowHeadersVisible = false;
            datagridviewss.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewss.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewss.RowTemplate.Height = 25;
            datagridviewss.ScrollBars = ScrollBars.None;
            datagridviewss.SelectedIndex = -1;
            datagridviewss.Size = new Size(294, 322);
            datagridviewss.TabIndex = 128;
            datagridviewss.CellValidating += datagridviewss_CellValidating;
            datagridviewss.CellValueChanged += datagridviewss_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxsstq
            // 
            checkboxsstq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsstq.Location = new Point(607, 186);
            checkboxsstq.Name = "checkboxsstq";
            checkboxsstq.Size = new Size(131, 23);
            checkboxsstq.TabIndex = 127;
            checkboxsstq.Text = "自动吐球";
            checkboxsstq.CheckedChanged += checkboxsstq_CheckedChanged;
            // 
            // textboxssjcfd1
            // 
            textboxssjcfd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxssjcfd1.Location = new Point(125, 231);
            textboxssjcfd1.Margin = new Padding(4, 5, 4, 5);
            textboxssjcfd1.MinimumSize = new Size(1, 16);
            textboxssjcfd1.Name = "textboxssjcfd1";
            textboxssjcfd1.Padding = new Padding(5);
            textboxssjcfd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxssjcfd1.ShowText = false;
            textboxssjcfd1.Size = new Size(70, 30);
            textboxssjcfd1.TabIndex = 126;
            textboxssjcfd1.Text = "0";
            textboxssjcfd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxssjcfd1.Watermark = "";
            textboxssjcfd1.TextChanged += textboxssjcfd1_TextChanged;
            textboxssjcfd1.KeyPress += textboxssjcfd2_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(19, 235);
            label1.Name = "label1";
            label1.Size = new Size(109, 23);
            label1.TabIndex = 125;
            label1.Text = "交叉幅度1";
            // 
            // comboboxsheshou
            // 
            comboboxsheshou.DataSource = null;
            comboboxsheshou.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxsheshou.DropDownWidth = 70;
            comboboxsheshou.FillColor = Color.White;
            comboboxsheshou.FillColorGradient = true;
            comboboxsheshou.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxsheshou.ItemHeight = 40;
            comboboxsheshou.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxsheshou.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxsheshou.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxsheshou.Location = new Point(125, 180);
            comboboxsheshou.Margin = new Padding(0);
            comboboxsheshou.MaxDropDownItems = 30;
            comboboxsheshou.MinimumSize = new Size(63, 0);
            comboboxsheshou.Name = "comboboxsheshou";
            comboboxsheshou.Padding = new Padding(0, 0, 30, 10);
            comboboxsheshou.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxsheshou.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxsheshou.ScrollBarStyleInherited = false;
            comboboxsheshou.Size = new Size(70, 31);
            comboboxsheshou.SymbolSize = 24;
            comboboxsheshou.TabIndex = 124;
            comboboxsheshou.Text = "A";
            comboboxsheshou.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxsheshou.Watermark = "";
            comboboxsheshou.SelectedIndexChanged += comboboxsheshou_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 121;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 123;
            label7.Text = "快捷键";
            // 
            // checkboxsheshou
            // 
            checkboxsheshou.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsheshou.Location = new Point(208, 184);
            checkboxsheshou.Name = "checkboxsheshou";
            checkboxsheshou.Size = new Size(95, 23);
            checkboxsheshou.TabIndex = 122;
            checkboxsheshou.Text = "启用";
            checkboxsheshou.CheckedChanged += checkboxsheshou_CheckedChanged;
            // 
            // buttonsscz
            // 
            buttonsscz.BackHover = Color.Aquamarine;
            buttonsscz.DefaultBack = Color.Azure;
            buttonsscz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonsscz.Location = new Point(208, 231);
            buttonsscz.Name = "buttonsscz";
            buttonsscz.Size = new Size(95, 64);
            buttonsscz.TabIndex = 143;
            buttonsscz.Text = "重置";
            buttonsscz.Click += buttonsscz_Click;
            // 
            // button1
            // 
            button1.BackHover = Color.Aquamarine;
            button1.DefaultBack = Color.Azure;
            button1.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            button1.Location = new Point(208, 301);
            button1.Name = "button1";
            button1.Size = new Size(95, 93);
            button1.TabIndex = 144;
            button1.Text = "旋转\r\n拐弯";
            button1.Click += button1_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "遥杆方向决定蛇手最终合球方向，鼠标相对于遥杆哪边就往哪边拐";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // sheshou
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(button1);
            Controls.Add(buttonsscz);
            Controls.Add(textboxssjd4);
            Controls.Add(label8);
            Controls.Add(textboxssjcfd4);
            Controls.Add(label9);
            Controls.Add(textboxssjd3);
            Controls.Add(label5);
            Controls.Add(textboxssjcfd3);
            Controls.Add(label6);
            Controls.Add(textboxssjd2);
            Controls.Add(label3);
            Controls.Add(textboxssjcfd2);
            Controls.Add(label4);
            Controls.Add(textboxssjd1);
            Controls.Add(label2);
            Controls.Add(datagridviewss);
            Controls.Add(checkboxsstq);
            Controls.Add(textboxssjcfd1);
            Controls.Add(label1);
            Controls.Add(comboboxsheshou);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxsheshou);
            FormBorderStyle = FormBorderStyle.None;
            Name = "sheshou";
            Text = "sheshou";
            Load += sheshou_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewss).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewss;
        public Sunny.UI.UITextBox textboxssjd4;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxssjcfd4;
        public AntdUI.Label label9;
        public Sunny.UI.UITextBox textboxssjd3;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxssjcfd3;
        public AntdUI.Label label6;
        public Sunny.UI.UITextBox textboxssjd2;
        public AntdUI.Label label3;
        public Sunny.UI.UITextBox textboxssjcfd2;
        public AntdUI.Label label4;
        public Sunny.UI.UITextBox textboxssjd1;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxsstq;
        public Sunny.UI.UITextBox textboxssjcfd1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxsheshou;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxsheshou;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonsscz;
        public AntdUI.Button button1;
        public AntdUI.Label label19;
    }
}