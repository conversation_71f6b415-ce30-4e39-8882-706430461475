﻿namespace ANYE_Balls
{
    partial class heipingkadian
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            uiGroupBox1 = new Sunny.UI.UIGroupBox();
            label19 = new AntdUI.Label();
            uiGroupBox2 = new Sunny.UI.UIGroupBox();
            uiTextBox2 = new Sunny.UI.UITextBox();
            button2 = new AntdUI.Button();
            button1 = new AntdUI.Button();
            checkboxheipingfx = new AntdUI.Checkbox();
            textboxhpglbc = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxhpcsbl = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxhpfsyc = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            textboxhpfscs = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            checkboxheiping = new AntdUI.Checkbox();
            comboBoxheipingkjj = new Sunny.UI.UIComboBox();
            label2 = new AntdUI.Label();
            buttonheipingcsh = new AntdUI.Button();
            uiGroupBox3 = new Sunny.UI.UIGroupBox();
            uiTextBox1 = new Sunny.UI.UITextBox();
            checkbox1 = new AntdUI.Checkbox();
            uiTextBox5 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            uiTextBox6 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            checkbox2 = new AntdUI.Checkbox();
            uiComboBox1 = new Sunny.UI.UIComboBox();
            label10 = new AntdUI.Label();
            button5 = new AntdUI.Button();
            uiGroupBox1.SuspendLayout();
            uiGroupBox2.SuspendLayout();
            uiGroupBox3.SuspendLayout();
            SuspendLayout();
            // 
            // uiGroupBox1
            // 
            uiGroupBox1.Controls.Add(label19);
            uiGroupBox1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox1.Location = new Point(14, 5);
            uiGroupBox1.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox1.MinimumSize = new Size(1, 1);
            uiGroupBox1.Name = "uiGroupBox1";
            uiGroupBox1.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox1.Radius = 20;
            uiGroupBox1.RectColor = SystemColors.ActiveCaption;
            uiGroupBox1.Size = new Size(753, 127);
            uiGroupBox1.TabIndex = 43;
            uiGroupBox1.Text = "黑屏卡点注意事项";
            uiGroupBox1.TextAlignment = ContentAlignment.MiddleLeft;
            uiGroupBox1.Click += uiGroupBox1_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(19, 35);
            label19.Name = "label19";
            label19.Size = new Size(718, 77);
            label19.TabIndex = 54;
            label19.Text = "需单独初始化。启用后，将鼠标放在要卡点的球体上，按下快捷键，出现红圈，滚轮可调节红圈大小，若检测到鼠标所在球体体积小于红圈，则立刻执行分身。\r\n注：1.请勿将鼠标触碰到任何纯黑区域，包括纯黑皮肤，纯黑名字。可以开启去皮，和修改昵称大小来弥补黑屏的缺陷。";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // uiGroupBox2
            // 
            uiGroupBox2.Controls.Add(uiTextBox2);
            uiGroupBox2.Controls.Add(button2);
            uiGroupBox2.Controls.Add(button1);
            uiGroupBox2.Controls.Add(checkboxheipingfx);
            uiGroupBox2.Controls.Add(textboxhpglbc);
            uiGroupBox2.Controls.Add(label5);
            uiGroupBox2.Controls.Add(textboxhpcsbl);
            uiGroupBox2.Controls.Add(label4);
            uiGroupBox2.Controls.Add(textboxhpfsyc);
            uiGroupBox2.Controls.Add(label1);
            uiGroupBox2.Controls.Add(textboxhpfscs);
            uiGroupBox2.Controls.Add(label3);
            uiGroupBox2.Controls.Add(checkboxheiping);
            uiGroupBox2.Controls.Add(comboBoxheipingkjj);
            uiGroupBox2.Controls.Add(label2);
            uiGroupBox2.Controls.Add(buttonheipingcsh);
            uiGroupBox2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox2.Location = new Point(14, 133);
            uiGroupBox2.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox2.MinimumSize = new Size(1, 1);
            uiGroupBox2.Name = "uiGroupBox2";
            uiGroupBox2.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox2.Radius = 20;
            uiGroupBox2.RectColor = SystemColors.ActiveCaption;
            uiGroupBox2.Size = new Size(753, 169);
            uiGroupBox2.TabIndex = 44;
            uiGroupBox2.Text = "黑屏卡点参数";
            uiGroupBox2.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiTextBox2
            // 
            uiTextBox2.Font = new Font("Microsoft YaHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox2.Location = new Point(571, 40);
            uiTextBox2.Margin = new Padding(4, 5, 4, 5);
            uiTextBox2.MaxLength = 1000;
            uiTextBox2.MinimumSize = new Size(1, 16);
            uiTextBox2.Name = "uiTextBox2";
            uiTextBox2.Padding = new Padding(5);
            uiTextBox2.ShowText = false;
            uiTextBox2.Size = new Size(132, 31);
            uiTextBox2.TabIndex = 64;
            uiTextBox2.Text = "0";
            uiTextBox2.TextAlignment = ContentAlignment.MiddleCenter;
            uiTextBox2.Visible = false;
            uiTextBox2.Watermark = "";
            // 
            // button2
            // 
            button2.BackHover = Color.Aquamarine;
            button2.DefaultBack = Color.Azure;
            button2.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            button2.Location = new Point(571, 112);
            button2.Name = "button2";
            button2.Size = new Size(137, 40);
            button2.TabIndex = 63;
            button2.Text = "恢复";
            button2.Click += button2_Click;
            // 
            // button1
            // 
            button1.BackHover = Color.Aquamarine;
            button1.DefaultBack = Color.Azure;
            button1.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            button1.Location = new Point(428, 112);
            button1.Name = "button1";
            button1.Size = new Size(137, 40);
            button1.TabIndex = 62;
            button1.Text = "仅背景黑底";
            button1.Click += button1_Click;
            // 
            // checkboxheipingfx
            // 
            checkboxheipingfx.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxheipingfx.Location = new Point(407, 44);
            checkboxheipingfx.Name = "checkboxheipingfx";
            checkboxheipingfx.Size = new Size(132, 23);
            checkboxheipingfx.TabIndex = 61;
            checkboxheipingfx.Text = "记录方向";
            checkboxheipingfx.Visible = false;
            // 
            // textboxhpglbc
            // 
            textboxhpglbc.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxhpglbc.Location = new Point(314, 122);
            textboxhpglbc.Margin = new Padding(4, 5, 4, 5);
            textboxhpglbc.MinimumSize = new Size(1, 16);
            textboxhpglbc.Name = "textboxhpglbc";
            textboxhpglbc.Padding = new Padding(5);
            textboxhpglbc.RectColor = Color.FromArgb(255, 255, 192);
            textboxhpglbc.ShowText = false;
            textboxhpglbc.Size = new Size(77, 30);
            textboxhpglbc.TabIndex = 60;
            textboxhpglbc.Text = "0";
            textboxhpglbc.TextAlignment = ContentAlignment.MiddleCenter;
            textboxhpglbc.Watermark = "";
            textboxhpglbc.TextChanged += textboxhpglbc_TextChanged;
            textboxhpglbc.KeyPress += textboxhpcsbl_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(216, 127);
            label5.Name = "label5";
            label5.Size = new Size(94, 23);
            label5.TabIndex = 59;
            label5.Text = "滚轮步长";
            // 
            // textboxhpcsbl
            // 
            textboxhpcsbl.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxhpcsbl.Location = new Point(117, 122);
            textboxhpcsbl.Margin = new Padding(4, 5, 4, 5);
            textboxhpcsbl.MinimumSize = new Size(1, 16);
            textboxhpcsbl.Name = "textboxhpcsbl";
            textboxhpcsbl.Padding = new Padding(5);
            textboxhpcsbl.RectColor = Color.FromArgb(255, 255, 192);
            textboxhpcsbl.ShowText = false;
            textboxhpcsbl.Size = new Size(77, 30);
            textboxhpcsbl.TabIndex = 58;
            textboxhpcsbl.Text = "0";
            textboxhpcsbl.TextAlignment = ContentAlignment.MiddleCenter;
            textboxhpcsbl.Watermark = "";
            textboxhpcsbl.TextChanged += textboxhpcsbl_TextChanged;
            textboxhpcsbl.KeyPress += textboxhpcsbl_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 127);
            label4.Name = "label4";
            label4.Size = new Size(94, 23);
            label4.TabIndex = 57;
            label4.Text = "初始比例";
            // 
            // textboxhpfsyc
            // 
            textboxhpfsyc.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxhpfsyc.Location = new Point(314, 81);
            textboxhpfsyc.Margin = new Padding(4, 5, 4, 5);
            textboxhpfsyc.MinimumSize = new Size(1, 16);
            textboxhpfsyc.Name = "textboxhpfsyc";
            textboxhpfsyc.Padding = new Padding(5);
            textboxhpfsyc.RectColor = Color.FromArgb(255, 255, 192);
            textboxhpfsyc.ShowText = false;
            textboxhpfsyc.Size = new Size(77, 30);
            textboxhpfsyc.TabIndex = 56;
            textboxhpfsyc.Text = "0";
            textboxhpfsyc.TextAlignment = ContentAlignment.MiddleCenter;
            textboxhpfsyc.Watermark = "";
            textboxhpfsyc.TextChanged += textboxhpfsyc_TextChanged;
            textboxhpfsyc.KeyPress += textboxhpcsbl_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(216, 86);
            label1.Name = "label1";
            label1.Size = new Size(94, 23);
            label1.TabIndex = 55;
            label1.Text = "分身延迟";
            // 
            // textboxhpfscs
            // 
            textboxhpfscs.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxhpfscs.Location = new Point(117, 81);
            textboxhpfscs.Margin = new Padding(4, 5, 4, 5);
            textboxhpfscs.MinimumSize = new Size(1, 16);
            textboxhpfscs.Name = "textboxhpfscs";
            textboxhpfscs.Padding = new Padding(5);
            textboxhpfscs.RectColor = Color.FromArgb(255, 255, 192);
            textboxhpfscs.ShowText = false;
            textboxhpfscs.Size = new Size(77, 30);
            textboxhpfscs.TabIndex = 54;
            textboxhpfscs.Text = "0";
            textboxhpfscs.TextAlignment = ContentAlignment.MiddleCenter;
            textboxhpfscs.Watermark = "";
            textboxhpfscs.TextChanged += textboxhpfscs_TextChanged;
            textboxhpfscs.KeyPress += textboxhpcsbl_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(19, 86);
            label3.Name = "label3";
            label3.Size = new Size(94, 23);
            label3.TabIndex = 53;
            label3.Text = "分身次数";
            // 
            // checkboxheiping
            // 
            checkboxheiping.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxheiping.Location = new Point(300, 44);
            checkboxheiping.Name = "checkboxheiping";
            checkboxheiping.Size = new Size(101, 23);
            checkboxheiping.TabIndex = 45;
            checkboxheiping.Text = "启用";
            checkboxheiping.CheckedChanged += checkboxheiping_CheckedChanged;
            // 
            // comboBoxheipingkjj
            // 
            comboBoxheipingkjj.DataSource = null;
            comboBoxheipingkjj.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxheipingkjj.DropDownWidth = 70;
            comboBoxheipingkjj.FillColor = Color.White;
            comboBoxheipingkjj.FillColorGradient = true;
            comboBoxheipingkjj.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxheipingkjj.ItemHeight = 40;
            comboBoxheipingkjj.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxheipingkjj.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxheipingkjj.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxheipingkjj.Location = new Point(227, 40);
            comboBoxheipingkjj.Margin = new Padding(0);
            comboBoxheipingkjj.MaxDropDownItems = 30;
            comboBoxheipingkjj.MinimumSize = new Size(63, 0);
            comboBoxheipingkjj.Name = "comboBoxheipingkjj";
            comboBoxheipingkjj.Padding = new Padding(0, 0, 30, 10);
            comboBoxheipingkjj.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxheipingkjj.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxheipingkjj.ScrollBarStyleInherited = false;
            comboBoxheipingkjj.Size = new Size(70, 31);
            comboBoxheipingkjj.SymbolSize = 24;
            comboBoxheipingkjj.TabIndex = 44;
            comboBoxheipingkjj.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxheipingkjj.Watermark = "";
            comboBoxheipingkjj.SelectedIndexChanged += comboBoxheipingkjj_SelectedIndexChanged;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(139, 44);
            label2.Name = "label2";
            label2.Size = new Size(94, 23);
            label2.TabIndex = 43;
            label2.Text = "快捷键：";
            // 
            // buttonheipingcsh
            // 
            buttonheipingcsh.BackHover = Color.Aquamarine;
            buttonheipingcsh.DefaultBack = Color.Azure;
            buttonheipingcsh.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonheipingcsh.Location = new Point(15, 35);
            buttonheipingcsh.Name = "buttonheipingcsh";
            buttonheipingcsh.Size = new Size(111, 40);
            buttonheipingcsh.TabIndex = 18;
            buttonheipingcsh.Text = "初始化";
            buttonheipingcsh.Click += buttonfeipingcsh_Click;
            // 
            // uiGroupBox3
            // 
            uiGroupBox3.Controls.Add(uiTextBox1);
            uiGroupBox3.Controls.Add(checkbox1);
            uiGroupBox3.Controls.Add(uiTextBox5);
            uiGroupBox3.Controls.Add(label8);
            uiGroupBox3.Controls.Add(uiTextBox6);
            uiGroupBox3.Controls.Add(label9);
            uiGroupBox3.Controls.Add(checkbox2);
            uiGroupBox3.Controls.Add(uiComboBox1);
            uiGroupBox3.Controls.Add(label10);
            uiGroupBox3.Controls.Add(button5);
            uiGroupBox3.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox3.Location = new Point(13, 304);
            uiGroupBox3.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox3.MinimumSize = new Size(1, 1);
            uiGroupBox3.Name = "uiGroupBox3";
            uiGroupBox3.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox3.Radius = 20;
            uiGroupBox3.RectColor = SystemColors.ActiveCaption;
            uiGroupBox3.Size = new Size(753, 125);
            uiGroupBox3.TabIndex = 45;
            uiGroupBox3.Text = "图色卡点参数";
            uiGroupBox3.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // uiTextBox1
            // 
            uiTextBox1.Font = new Font("Microsoft YaHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox1.Location = new Point(571, 40);
            uiTextBox1.Margin = new Padding(4, 5, 4, 5);
            uiTextBox1.MaxLength = 1000;
            uiTextBox1.MinimumSize = new Size(1, 16);
            uiTextBox1.Name = "uiTextBox1";
            uiTextBox1.Padding = new Padding(5);
            uiTextBox1.ShowText = false;
            uiTextBox1.Size = new Size(132, 31);
            uiTextBox1.TabIndex = 64;
            uiTextBox1.Text = "0";
            uiTextBox1.TextAlignment = ContentAlignment.MiddleCenter;
            uiTextBox1.Visible = false;
            uiTextBox1.Watermark = "";
            // 
            // checkbox1
            // 
            checkbox1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkbox1.Location = new Point(407, 44);
            checkbox1.Name = "checkbox1";
            checkbox1.Size = new Size(132, 23);
            checkbox1.TabIndex = 61;
            checkbox1.Text = "记录方向";
            checkbox1.Visible = false;
            // 
            // uiTextBox5
            // 
            uiTextBox5.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox5.Location = new Point(314, 81);
            uiTextBox5.Margin = new Padding(4, 5, 4, 5);
            uiTextBox5.MinimumSize = new Size(1, 16);
            uiTextBox5.Name = "uiTextBox5";
            uiTextBox5.Padding = new Padding(5);
            uiTextBox5.RectColor = Color.FromArgb(255, 255, 192);
            uiTextBox5.ShowText = false;
            uiTextBox5.Size = new Size(77, 30);
            uiTextBox5.TabIndex = 56;
            uiTextBox5.Text = "0";
            uiTextBox5.TextAlignment = ContentAlignment.MiddleCenter;
            uiTextBox5.Watermark = "";
            uiTextBox5.TextChanged += uiTextBox5_TextChanged;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(216, 86);
            label8.Name = "label8";
            label8.Size = new Size(94, 23);
            label8.TabIndex = 55;
            label8.Text = "分身延迟";
            // 
            // uiTextBox6
            // 
            uiTextBox6.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            uiTextBox6.Location = new Point(117, 81);
            uiTextBox6.Margin = new Padding(4, 5, 4, 5);
            uiTextBox6.MinimumSize = new Size(1, 16);
            uiTextBox6.Name = "uiTextBox6";
            uiTextBox6.Padding = new Padding(5);
            uiTextBox6.RectColor = Color.FromArgb(255, 255, 192);
            uiTextBox6.ShowText = false;
            uiTextBox6.Size = new Size(77, 30);
            uiTextBox6.TabIndex = 54;
            uiTextBox6.Text = "0";
            uiTextBox6.TextAlignment = ContentAlignment.MiddleCenter;
            uiTextBox6.Watermark = "";
            uiTextBox6.TipsClick += uiTextBox6_TipsClick;
            uiTextBox6.TextChanged += uiTextBox6_TextChanged;
            uiTextBox6.TextAlignmentChange += uiTextBox6_TextAlignmentChange;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 86);
            label9.Name = "label9";
            label9.Size = new Size(94, 23);
            label9.TabIndex = 53;
            label9.Text = "分身次数";
            // 
            // checkbox2
            // 
            checkbox2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkbox2.Location = new Point(300, 44);
            checkbox2.Name = "checkbox2";
            checkbox2.Size = new Size(101, 23);
            checkbox2.TabIndex = 45;
            checkbox2.Text = "启用";
            checkbox2.CheckedChanged += checkbox2_CheckedChanged;
            // 
            // uiComboBox1
            // 
            uiComboBox1.DataSource = null;
            uiComboBox1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            uiComboBox1.DropDownWidth = 70;
            uiComboBox1.FillColor = Color.White;
            uiComboBox1.FillColorGradient = true;
            uiComboBox1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            uiComboBox1.ItemHeight = 40;
            uiComboBox1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            uiComboBox1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            uiComboBox1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            uiComboBox1.Location = new Point(227, 40);
            uiComboBox1.Margin = new Padding(0);
            uiComboBox1.MaxDropDownItems = 30;
            uiComboBox1.MinimumSize = new Size(63, 0);
            uiComboBox1.Name = "uiComboBox1";
            uiComboBox1.Padding = new Padding(0, 0, 30, 10);
            uiComboBox1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            uiComboBox1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            uiComboBox1.ScrollBarStyleInherited = false;
            uiComboBox1.Size = new Size(70, 31);
            uiComboBox1.SymbolSize = 24;
            uiComboBox1.TabIndex = 44;
            uiComboBox1.TextAlignment = ContentAlignment.MiddleLeft;
            uiComboBox1.Watermark = "";
            uiComboBox1.SelectedIndexChanged += uiComboBox1_SelectedIndexChanged;
            // 
            // label10
            // 
            label10.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label10.Location = new Point(139, 44);
            label10.Name = "label10";
            label10.Size = new Size(94, 23);
            label10.TabIndex = 43;
            label10.Text = "快捷键：";
            // 
            // button5
            // 
            button5.BackHover = Color.Aquamarine;
            button5.DefaultBack = Color.Azure;
            button5.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            button5.Location = new Point(15, 35);
            button5.Name = "button5";
            button5.Size = new Size(111, 40);
            button5.TabIndex = 18;
            button5.Text = "初始化";
            button5.Click += button5_Click;
            // 
            // heipingkadian
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(uiGroupBox3);
            Controls.Add(uiGroupBox2);
            Controls.Add(uiGroupBox1);
            FormBorderStyle = FormBorderStyle.None;
            Name = "heipingkadian";
            Text = "heipingkadian";
            Load += heipingkadian_Load;
            uiGroupBox1.ResumeLayout(false);
            uiGroupBox2.ResumeLayout(false);
            uiGroupBox3.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        public Sunny.UI.UIGroupBox uiGroupBox1;
        public Sunny.UI.UIGroupBox uiGroupBox2;
        public AntdUI.Button buttonheipingcsh;
        public AntdUI.Label label2;
        public Sunny.UI.UIComboBox comboBoxheipingkjj;
        public AntdUI.Checkbox checkboxheiping;
        public Sunny.UI.UITextBox textboxhpfsyc;
        public AntdUI.Label label1;
        public Sunny.UI.UITextBox textboxhpfscs;
        public AntdUI.Label label3;
        public AntdUI.Checkbox checkboxheipingfx;
        public Sunny.UI.UITextBox textboxhpglbc;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxhpcsbl;
        public AntdUI.Label label4;
        public AntdUI.Label label19;
        public AntdUI.Button button2;
        public AntdUI.Button button1;
        private Sunny.UI.UITextBox uiTextBox2;
        public Sunny.UI.UIGroupBox uiGroupBox3;
        private Sunny.UI.UITextBox uiTextBox1;
        public AntdUI.Checkbox checkbox1;
        public Sunny.UI.UITextBox uiTextBox5;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox uiTextBox6;
        public AntdUI.Label label9;
        public AntdUI.Checkbox checkbox2;
        public Sunny.UI.UIComboBox uiComboBox1;
        public AntdUI.Label label10;
        public AntdUI.Button button5;
    }
}