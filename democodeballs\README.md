# 球球大作战合球算法库

这个项目提取了球球大作战中各种合球算法的核心实现，去除了GUI依赖，便于学习和理解。

## 项目结构

```
democodeballs/
├── README.md               - 本文件，项目说明
├── main.c                  - 示例使用代码
├── common_defs.h           - 通用定义和结构体
├── math_utils.c/h          - 数学计算工具函数
├── input_simulator.c/h     - 输入模拟功能
├── memory_utils.c/h        - 内存读写功能
├── triangle_merge.c/h      - 三角合球算法
├── center_split.c/h        - 中心分身算法
├── side_merge.c/h          - 侧合算法
├── rotate_merge.c/h        - 旋转合球算法
├── semi_rotate_merge.c/h   - 半旋合球算法
└── snake_hand.c/h          - 蛇手合球算法
```

## 算法原理

### 三角合球 (Triangle Merge)
三角合球是一种基础的合球技巧，通过在三个方向上分裂细胞并快速合并来实现。其核心原理是：
1. 确定鼠标指向的方向向量
2. 计算与该方向成特定角度的三个点
3. 依次移动鼠标到这三个点并触发分身
4. 最后回到原点完成合并

### 中心分身 (Center Split)
中心分身算法通过在原地分裂细胞并快速合并来实现。其特点是：
1. 在原地按下鼠标
2. 触发分身
3. 小幅度移动鼠标（通常是微调位置）
4. 再次分身并快速合并

### 侧合 (Side Merge)
侧合是一种在侧面进行合球的技术，通过：
1. 计算垂直于主方向的向量
2. 向侧面移动并分身
3. 快速回到原方向完成合并

### 旋转合球 (Rotate Merge)
旋转合球通过在多个角度点上分裂细胞并快速合并，形成旋转效果：
1. 计算初始角度
2. 按照设定的角度增量，依次在不同角度点上分身
3. 最后回到合适位置完成合并

### 半旋合球 (Semi-rotate Merge)
半旋合球是旋转合球的变种，只在部分角度上进行分裂：
1. 计算初始角度
2. 在有限的角度范围内进行分身
3. 最后合并形成半旋效果

### 蛇手合球 (Snake Hand)
蛇手合球是一种高级技巧，通过模拟蛇形移动路径来实现：
1. 计算初始方向
2. 按照预设的路径点移动鼠标
3. 在关键点上分身
4. 最后回到合适位置完成合并

## 使用方法

1. 包含需要的头文件
2. 初始化输入模拟器
3. 设置合球参数（角度、距离、延迟等）
4. 调用相应的合球函数

示例代码见 `main.c`。

## 注意事项

1. 所有算法都需要适当的延迟参数才能正常工作
2. 参数需要根据游戏窗口大小和分辨率进行调整
3. 算法效果受网络延迟影响 