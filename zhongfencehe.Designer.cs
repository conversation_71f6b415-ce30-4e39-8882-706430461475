﻿namespace ANYE_Balls
{
    partial class zhongfencehe
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewzfch = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxzfchtq = new AntdUI.Checkbox();
            textboxzfchjcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxzhongfencehe = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxzhongfencehe = new AntdUI.Checkbox();
            buttonzfchcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewzfch).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewzfch
            // 
            datagridviewzfch.AllowUserToAddRows = false;
            datagridviewzfch.AllowUserToDeleteRows = false;
            datagridviewzfch.AllowUserToResizeColumns = false;
            datagridviewzfch.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewzfch.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewzfch.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewzfch.BorderStyle = BorderStyle.Fixed3D;
            datagridviewzfch.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewzfch.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewzfch.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewzfch.ColumnHeadersHeight = 40;
            datagridviewzfch.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewzfch.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewzfch.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewzfch.EnableHeadersVisualStyles = false;
            datagridviewzfch.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewzfch.GridColor = Color.AliceBlue;
            datagridviewzfch.Location = new Point(309, 184);
            datagridviewzfch.Name = "datagridviewzfch";
            datagridviewzfch.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewzfch.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewzfch.RowHeadersVisible = false;
            datagridviewzfch.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewzfch.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewzfch.RowTemplate.Height = 25;
            datagridviewzfch.ScrollBars = ScrollBars.None;
            datagridviewzfch.SelectedIndex = -1;
            datagridviewzfch.Size = new Size(294, 300);
            datagridviewzfch.TabIndex = 66;
            datagridviewzfch.CellValidating += datagridviewzfch_CellValidating;
            datagridviewzfch.CellValueChanged += datagridviewzfch_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxzfchtq
            // 
            checkboxzfchtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxzfchtq.Location = new Point(607, 186);
            checkboxzfchtq.Name = "checkboxzfchtq";
            checkboxzfchtq.Size = new Size(131, 23);
            checkboxzfchtq.TabIndex = 65;
            checkboxzfchtq.Text = "自动吐球";
            checkboxzfchtq.CheckedChanged += checkboxzfchtq_CheckedChanged;
            // 
            // textboxzfchjcfd
            // 
            textboxzfchjcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxzfchjcfd.Location = new Point(125, 231);
            textboxzfchjcfd.Margin = new Padding(4, 5, 4, 5);
            textboxzfchjcfd.MinimumSize = new Size(1, 16);
            textboxzfchjcfd.Name = "textboxzfchjcfd";
            textboxzfchjcfd.Padding = new Padding(5);
            textboxzfchjcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxzfchjcfd.ShowText = false;
            textboxzfchjcfd.Size = new Size(70, 30);
            textboxzfchjcfd.TabIndex = 64;
            textboxzfchjcfd.Text = "0";
            textboxzfchjcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxzfchjcfd.Watermark = "";
            textboxzfchjcfd.TextChanged += textboxzfchjcfd_TextChanged;
            textboxzfchjcfd.KeyPress += textboxzfchjcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 63;
            label1.Text = "交叉幅度";
            // 
            // comboboxzhongfencehe
            // 
            comboboxzhongfencehe.DataSource = null;
            comboboxzhongfencehe.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxzhongfencehe.DropDownWidth = 70;
            comboboxzhongfencehe.FillColor = Color.White;
            comboboxzhongfencehe.FillColorGradient = true;
            comboboxzhongfencehe.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxzhongfencehe.ItemHeight = 40;
            comboboxzhongfencehe.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxzhongfencehe.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxzhongfencehe.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxzhongfencehe.Location = new Point(125, 180);
            comboboxzhongfencehe.Margin = new Padding(0);
            comboboxzhongfencehe.MaxDropDownItems = 30;
            comboboxzhongfencehe.MinimumSize = new Size(63, 0);
            comboboxzhongfencehe.Name = "comboboxzhongfencehe";
            comboboxzhongfencehe.Padding = new Padding(0, 0, 30, 10);
            comboboxzhongfencehe.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxzhongfencehe.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxzhongfencehe.ScrollBarStyleInherited = false;
            comboboxzhongfencehe.Size = new Size(70, 31);
            comboboxzhongfencehe.SymbolSize = 24;
            comboboxzhongfencehe.TabIndex = 62;
            comboboxzhongfencehe.Text = "A";
            comboboxzhongfencehe.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxzhongfencehe.Watermark = "";
            comboboxzhongfencehe.SelectedIndexChanged += comboboxzhongfencehe_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 59;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 61;
            label7.Text = "快捷键";
            // 
            // checkboxzhongfencehe
            // 
            checkboxzhongfencehe.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxzhongfencehe.Location = new Point(208, 184);
            checkboxzhongfencehe.Name = "checkboxzhongfencehe";
            checkboxzhongfencehe.Size = new Size(95, 23);
            checkboxzhongfencehe.TabIndex = 60;
            checkboxzhongfencehe.Text = "启用";
            checkboxzhongfencehe.CheckedChanged += checkboxzhongfencehe_CheckedChanged;
            // 
            // buttonzfchcz
            // 
            buttonzfchcz.BackHover = Color.Aquamarine;
            buttonzfchcz.DefaultBack = Color.Azure;
            buttonzfchcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonzfchcz.Location = new Point(208, 231);
            buttonzfchcz.Name = "buttonzfchcz";
            buttonzfchcz.Size = new Size(95, 64);
            buttonzfchcz.TabIndex = 67;
            buttonzfchcz.Text = "重置";
            buttonzfchcz.Click += buttonzfchcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标放在哪个位置就往哪个位置合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // zhongfencehe
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonzfchcz);
            Controls.Add(datagridviewzfch);
            Controls.Add(checkboxzfchtq);
            Controls.Add(textboxzfchjcfd);
            Controls.Add(label1);
            Controls.Add(comboboxzhongfencehe);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxzhongfencehe);
            FormBorderStyle = FormBorderStyle.None;
            Name = "zhongfencehe";
            Text = "zhongfencehe";
            Load += zhongfencehe_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewzfch).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewzfch;
        public AntdUI.Checkbox checkboxzfchtq;
        public Sunny.UI.UITextBox textboxzfchjcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxzhongfencehe;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxzhongfencehe;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonzfchcz;
        public AntdUI.Label label19;
    }
}