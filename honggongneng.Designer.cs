﻿namespace ANYE_Balls
{
    partial class honggongneng
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            Groupshiye = new Sunny.UI.UIGroupBox();
            textboxshiliufenhong = new Sunny.UI.UITextBox();
            checkBoxshiliufenhong = new AntdUI.Checkbox();
            label6 = new AntdUI.Label();
            comboBoxshiliufenhong = new Sunny.UI.UIComboBox();
            textboxbafenhong = new Sunny.UI.UITextBox();
            checkBoxbafenhong = new AntdUI.Checkbox();
            label5 = new AntdUI.Label();
            comboBoxbafenhong = new Sunny.UI.UIComboBox();
            textboxgangganhong = new Sunny.UI.UITextBox();
            checkBoxgangganhong = new AntdUI.Checkbox();
            label4 = new AntdUI.Label();
            comboBoxgangganhong = new Sunny.UI.UIComboBox();
            textboxyuandihong = new Sunny.UI.UITextBox();
            checkBoxyuandihong = new AntdUI.Checkbox();
            label3 = new AntdUI.Label();
            comboBoxyuandihong = new Sunny.UI.UIComboBox();
            textboxsifenhong = new Sunny.UI.UITextBox();
            checkBoxsifenhong = new AntdUI.Checkbox();
            label1 = new AntdUI.Label();
            comboBoxsifenhong = new Sunny.UI.UIComboBox();
            textboxerfenhong = new Sunny.UI.UITextBox();
            checkBoxerfenhong = new AntdUI.Checkbox();
            label2 = new AntdUI.Label();
            comboBoxerfenhong = new Sunny.UI.UIComboBox();
            uiGroupBox1 = new Sunny.UI.UIGroupBox();
            comboBoxchanganhongmnj2 = new Sunny.UI.UIComboBox();
            label13 = new AntdUI.Label();
            comboBoxchanganhongmnj1 = new Sunny.UI.UIComboBox();
            label14 = new AntdUI.Label();
            textboxchanganhong2 = new Sunny.UI.UITextBox();
            checkBoxchanganhong2 = new AntdUI.Checkbox();
            label11 = new AntdUI.Label();
            comboBoxchanganhong2 = new Sunny.UI.UIComboBox();
            textboxchanganhong1 = new Sunny.UI.UITextBox();
            checkBoxchanganhong1 = new AntdUI.Checkbox();
            label12 = new AntdUI.Label();
            comboBoxchanganhong1 = new Sunny.UI.UIComboBox();
            uiGroupBox2 = new Sunny.UI.UIGroupBox();
            comboBoxlianjihongmnj2 = new Sunny.UI.UIComboBox();
            label7 = new AntdUI.Label();
            comboBoxlianjihongmnj1 = new Sunny.UI.UIComboBox();
            label8 = new AntdUI.Label();
            textboxlianjihong2 = new Sunny.UI.UITextBox();
            checkBoxlianjihong2 = new AntdUI.Checkbox();
            label9 = new AntdUI.Label();
            comboBoxlianjihong2 = new Sunny.UI.UIComboBox();
            textboxlianjihong1 = new Sunny.UI.UITextBox();
            checkBoxlianjihong1 = new AntdUI.Checkbox();
            label10 = new AntdUI.Label();
            comboBoxlianjihong1 = new Sunny.UI.UIComboBox();
            Groupshiye.SuspendLayout();
            uiGroupBox1.SuspendLayout();
            uiGroupBox2.SuspendLayout();
            SuspendLayout();
            // 
            // Groupshiye
            // 
            Groupshiye.Controls.Add(textboxshiliufenhong);
            Groupshiye.Controls.Add(checkBoxshiliufenhong);
            Groupshiye.Controls.Add(label6);
            Groupshiye.Controls.Add(comboBoxshiliufenhong);
            Groupshiye.Controls.Add(textboxbafenhong);
            Groupshiye.Controls.Add(checkBoxbafenhong);
            Groupshiye.Controls.Add(label5);
            Groupshiye.Controls.Add(comboBoxbafenhong);
            Groupshiye.Controls.Add(textboxgangganhong);
            Groupshiye.Controls.Add(checkBoxgangganhong);
            Groupshiye.Controls.Add(label4);
            Groupshiye.Controls.Add(comboBoxgangganhong);
            Groupshiye.Controls.Add(textboxyuandihong);
            Groupshiye.Controls.Add(checkBoxyuandihong);
            Groupshiye.Controls.Add(label3);
            Groupshiye.Controls.Add(comboBoxyuandihong);
            Groupshiye.Controls.Add(textboxsifenhong);
            Groupshiye.Controls.Add(checkBoxsifenhong);
            Groupshiye.Controls.Add(label1);
            Groupshiye.Controls.Add(comboBoxsifenhong);
            Groupshiye.Controls.Add(textboxerfenhong);
            Groupshiye.Controls.Add(checkBoxerfenhong);
            Groupshiye.Controls.Add(label2);
            Groupshiye.Controls.Add(comboBoxerfenhong);
            Groupshiye.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Groupshiye.Location = new Point(14, 5);
            Groupshiye.Margin = new Padding(4, 5, 4, 5);
            Groupshiye.MinimumSize = new Size(1, 1);
            Groupshiye.Name = "Groupshiye";
            Groupshiye.Padding = new Padding(0, 32, 0, 0);
            Groupshiye.Radius = 20;
            Groupshiye.RectColor = SystemColors.ActiveCaption;
            Groupshiye.Size = new Size(753, 311);
            Groupshiye.TabIndex = 41;
            Groupshiye.Text = "常用宏";
            Groupshiye.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // textboxshiliufenhong
            // 
            textboxshiliufenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxshiliufenhong.Location = new Point(298, 258);
            textboxshiliufenhong.Margin = new Padding(4, 5, 4, 5);
            textboxshiliufenhong.MinimumSize = new Size(1, 16);
            textboxshiliufenhong.Name = "textboxshiliufenhong";
            textboxshiliufenhong.Padding = new Padding(5);
            textboxshiliufenhong.RectColor = Color.FromArgb(255, 255, 192);
            textboxshiliufenhong.ShowText = false;
            textboxshiliufenhong.Size = new Size(77, 30);
            textboxshiliufenhong.TabIndex = 64;
            textboxshiliufenhong.Text = "0";
            textboxshiliufenhong.TextAlignment = ContentAlignment.MiddleCenter;
            textboxshiliufenhong.Watermark = "";
            textboxshiliufenhong.TextChanged += textBoxshiliufenhong_TextChanged;
            // 
            // checkBoxshiliufenhong
            // 
            checkBoxshiliufenhong.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxshiliufenhong.Location = new Point(20, 260);
            checkBoxshiliufenhong.Name = "checkBoxshiliufenhong";
            checkBoxshiliufenhong.Size = new Size(132, 23);
            checkBoxshiliufenhong.TabIndex = 63;
            checkBoxshiliufenhong.Text = "十六分宏";
            checkBoxshiliufenhong.CheckedChanged += checkBoxshiliufenhong_CheckedChanged;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(228, 263);
            label6.Name = "label6";
            label6.Size = new Size(76, 23);
            label6.TabIndex = 62;
            label6.Text = "间隔：";
            // 
            // comboBoxshiliufenhong
            // 
            comboBoxshiliufenhong.DataSource = null;
            comboBoxshiliufenhong.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxshiliufenhong.DropDownWidth = 70;
            comboBoxshiliufenhong.FillColor = Color.White;
            comboBoxshiliufenhong.FillColorGradient = true;
            comboBoxshiliufenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxshiliufenhong.ItemHeight = 40;
            comboBoxshiliufenhong.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxshiliufenhong.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxshiliufenhong.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxshiliufenhong.Location = new Point(155, 257);
            comboBoxshiliufenhong.Margin = new Padding(0);
            comboBoxshiliufenhong.MaxDropDownItems = 30;
            comboBoxshiliufenhong.MinimumSize = new Size(63, 0);
            comboBoxshiliufenhong.Name = "comboBoxshiliufenhong";
            comboBoxshiliufenhong.Padding = new Padding(0, 0, 30, 10);
            comboBoxshiliufenhong.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxshiliufenhong.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxshiliufenhong.ScrollBarStyleInherited = false;
            comboBoxshiliufenhong.Size = new Size(70, 31);
            comboBoxshiliufenhong.SymbolSize = 24;
            comboBoxshiliufenhong.TabIndex = 61;
            comboBoxshiliufenhong.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxshiliufenhong.Watermark = "";
            comboBoxshiliufenhong.SelectedIndexChanged += comboBoxshiliufenhong_SelectedIndexChanged;
            // 
            // textboxbafenhong
            // 
            textboxbafenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxbafenhong.Location = new Point(298, 215);
            textboxbafenhong.Margin = new Padding(4, 5, 4, 5);
            textboxbafenhong.MinimumSize = new Size(1, 16);
            textboxbafenhong.Name = "textboxbafenhong";
            textboxbafenhong.Padding = new Padding(5);
            textboxbafenhong.RectColor = Color.FromArgb(255, 255, 192);
            textboxbafenhong.ShowText = false;
            textboxbafenhong.Size = new Size(77, 30);
            textboxbafenhong.TabIndex = 60;
            textboxbafenhong.Text = "0";
            textboxbafenhong.TextAlignment = ContentAlignment.MiddleCenter;
            textboxbafenhong.Watermark = "";
            textboxbafenhong.TextChanged += textBoxbafenhong_TextChanged;
            // 
            // checkBoxbafenhong
            // 
            checkBoxbafenhong.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxbafenhong.Location = new Point(20, 217);
            checkBoxbafenhong.Name = "checkBoxbafenhong";
            checkBoxbafenhong.Size = new Size(132, 23);
            checkBoxbafenhong.TabIndex = 59;
            checkBoxbafenhong.Text = "八分宏";
            checkBoxbafenhong.CheckedChanged += checkBoxbafenhong_CheckedChanged;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(228, 220);
            label5.Name = "label5";
            label5.Size = new Size(76, 23);
            label5.TabIndex = 58;
            label5.Text = "间隔：";
            // 
            // comboBoxbafenhong
            // 
            comboBoxbafenhong.DataSource = null;
            comboBoxbafenhong.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxbafenhong.DropDownWidth = 70;
            comboBoxbafenhong.FillColor = Color.White;
            comboBoxbafenhong.FillColorGradient = true;
            comboBoxbafenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxbafenhong.ItemHeight = 40;
            comboBoxbafenhong.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxbafenhong.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxbafenhong.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxbafenhong.Location = new Point(155, 214);
            comboBoxbafenhong.Margin = new Padding(0);
            comboBoxbafenhong.MaxDropDownItems = 30;
            comboBoxbafenhong.MinimumSize = new Size(63, 0);
            comboBoxbafenhong.Name = "comboBoxbafenhong";
            comboBoxbafenhong.Padding = new Padding(0, 0, 30, 10);
            comboBoxbafenhong.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxbafenhong.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxbafenhong.ScrollBarStyleInherited = false;
            comboBoxbafenhong.Size = new Size(70, 31);
            comboBoxbafenhong.SymbolSize = 24;
            comboBoxbafenhong.TabIndex = 57;
            comboBoxbafenhong.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxbafenhong.Watermark = "";
            comboBoxbafenhong.SelectedIndexChanged += comboBoxbafenhong_SelectedIndexChanged;
            // 
            // textboxgangganhong
            // 
            textboxgangganhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxgangganhong.Location = new Point(298, 172);
            textboxgangganhong.Margin = new Padding(4, 5, 4, 5);
            textboxgangganhong.MinimumSize = new Size(1, 16);
            textboxgangganhong.Name = "textboxgangganhong";
            textboxgangganhong.Padding = new Padding(5);
            textboxgangganhong.RectColor = Color.FromArgb(255, 255, 192);
            textboxgangganhong.ShowText = false;
            textboxgangganhong.Size = new Size(77, 30);
            textboxgangganhong.TabIndex = 56;
            textboxgangganhong.Text = "0";
            textboxgangganhong.TextAlignment = ContentAlignment.MiddleCenter;
            textboxgangganhong.Watermark = "";
            textboxgangganhong.TextChanged += textBoxgangganhong_TextChanged;
            // 
            // checkBoxgangganhong
            // 
            checkBoxgangganhong.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxgangganhong.Location = new Point(20, 174);
            checkBoxgangganhong.Name = "checkBoxgangganhong";
            checkBoxgangganhong.Size = new Size(132, 23);
            checkBoxgangganhong.TabIndex = 55;
            checkBoxgangganhong.Text = "杠杆宏";
            checkBoxgangganhong.CheckedChanged += checkBoxgangganhong_CheckedChanged;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(228, 177);
            label4.Name = "label4";
            label4.Size = new Size(76, 23);
            label4.TabIndex = 54;
            label4.Text = "间隔：";
            // 
            // comboBoxgangganhong
            // 
            comboBoxgangganhong.DataSource = null;
            comboBoxgangganhong.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxgangganhong.DropDownWidth = 70;
            comboBoxgangganhong.FillColor = Color.White;
            comboBoxgangganhong.FillColorGradient = true;
            comboBoxgangganhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxgangganhong.ItemHeight = 40;
            comboBoxgangganhong.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxgangganhong.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxgangganhong.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxgangganhong.Location = new Point(155, 171);
            comboBoxgangganhong.Margin = new Padding(0);
            comboBoxgangganhong.MaxDropDownItems = 30;
            comboBoxgangganhong.MinimumSize = new Size(63, 0);
            comboBoxgangganhong.Name = "comboBoxgangganhong";
            comboBoxgangganhong.Padding = new Padding(0, 0, 30, 10);
            comboBoxgangganhong.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxgangganhong.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxgangganhong.ScrollBarStyleInherited = false;
            comboBoxgangganhong.Size = new Size(70, 31);
            comboBoxgangganhong.SymbolSize = 24;
            comboBoxgangganhong.TabIndex = 53;
            comboBoxgangganhong.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxgangganhong.Watermark = "";
            comboBoxgangganhong.SelectedIndexChanged += comboBoxgangganhong_SelectedIndexChanged;
            // 
            // textboxyuandihong
            // 
            textboxyuandihong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxyuandihong.Location = new Point(298, 129);
            textboxyuandihong.Margin = new Padding(4, 5, 4, 5);
            textboxyuandihong.MinimumSize = new Size(1, 16);
            textboxyuandihong.Name = "textboxyuandihong";
            textboxyuandihong.Padding = new Padding(5);
            textboxyuandihong.RectColor = Color.FromArgb(255, 255, 192);
            textboxyuandihong.ShowText = false;
            textboxyuandihong.Size = new Size(77, 30);
            textboxyuandihong.TabIndex = 52;
            textboxyuandihong.Text = "0";
            textboxyuandihong.TextAlignment = ContentAlignment.MiddleCenter;
            textboxyuandihong.Watermark = "";
            textboxyuandihong.TextChanged += textBoxyuandihong_TextChanged;
            // 
            // checkBoxyuandihong
            // 
            checkBoxyuandihong.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxyuandihong.Location = new Point(20, 131);
            checkBoxyuandihong.Name = "checkBoxyuandihong";
            checkBoxyuandihong.Size = new Size(132, 23);
            checkBoxyuandihong.TabIndex = 51;
            checkBoxyuandihong.Text = "原地宏";
            checkBoxyuandihong.CheckedChanged += checkBoxyuandihong_CheckedChanged;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(228, 134);
            label3.Name = "label3";
            label3.Size = new Size(76, 23);
            label3.TabIndex = 50;
            label3.Text = "间隔：";
            // 
            // comboBoxyuandihong
            // 
            comboBoxyuandihong.DataSource = null;
            comboBoxyuandihong.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxyuandihong.DropDownWidth = 70;
            comboBoxyuandihong.FillColor = Color.White;
            comboBoxyuandihong.FillColorGradient = true;
            comboBoxyuandihong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxyuandihong.ItemHeight = 40;
            comboBoxyuandihong.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxyuandihong.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxyuandihong.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxyuandihong.Location = new Point(155, 128);
            comboBoxyuandihong.Margin = new Padding(0);
            comboBoxyuandihong.MaxDropDownItems = 30;
            comboBoxyuandihong.MinimumSize = new Size(63, 0);
            comboBoxyuandihong.Name = "comboBoxyuandihong";
            comboBoxyuandihong.Padding = new Padding(0, 0, 30, 10);
            comboBoxyuandihong.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxyuandihong.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxyuandihong.ScrollBarStyleInherited = false;
            comboBoxyuandihong.Size = new Size(70, 31);
            comboBoxyuandihong.SymbolSize = 24;
            comboBoxyuandihong.TabIndex = 49;
            comboBoxyuandihong.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxyuandihong.Watermark = "";
            comboBoxyuandihong.SelectedIndexChanged += comboBoxyuandihong_SelectedIndexChanged;
            // 
            // textboxsifenhong
            // 
            textboxsifenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxsifenhong.Location = new Point(298, 86);
            textboxsifenhong.Margin = new Padding(4, 5, 4, 5);
            textboxsifenhong.MinimumSize = new Size(1, 16);
            textboxsifenhong.Name = "textboxsifenhong";
            textboxsifenhong.Padding = new Padding(5);
            textboxsifenhong.RectColor = Color.FromArgb(255, 255, 192);
            textboxsifenhong.ShowText = false;
            textboxsifenhong.Size = new Size(77, 30);
            textboxsifenhong.TabIndex = 48;
            textboxsifenhong.Text = "0";
            textboxsifenhong.TextAlignment = ContentAlignment.MiddleCenter;
            textboxsifenhong.Watermark = "";
            textboxsifenhong.TextChanged += textBoxsifenhong_TextChanged;
            // 
            // checkBoxsifenhong
            // 
            checkBoxsifenhong.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxsifenhong.Location = new Point(20, 88);
            checkBoxsifenhong.Name = "checkBoxsifenhong";
            checkBoxsifenhong.Size = new Size(132, 23);
            checkBoxsifenhong.TabIndex = 47;
            checkBoxsifenhong.Text = "四分宏";
            checkBoxsifenhong.CheckedChanged += checkBoxsifenhong_CheckedChanged;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(228, 91);
            label1.Name = "label1";
            label1.Size = new Size(76, 23);
            label1.TabIndex = 46;
            label1.Text = "间隔：";
            // 
            // comboBoxsifenhong
            // 
            comboBoxsifenhong.DataSource = null;
            comboBoxsifenhong.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxsifenhong.DropDownWidth = 70;
            comboBoxsifenhong.FillColor = Color.White;
            comboBoxsifenhong.FillColorGradient = true;
            comboBoxsifenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxsifenhong.ItemHeight = 40;
            comboBoxsifenhong.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxsifenhong.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxsifenhong.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxsifenhong.Location = new Point(155, 85);
            comboBoxsifenhong.Margin = new Padding(0);
            comboBoxsifenhong.MaxDropDownItems = 30;
            comboBoxsifenhong.MinimumSize = new Size(63, 0);
            comboBoxsifenhong.Name = "comboBoxsifenhong";
            comboBoxsifenhong.Padding = new Padding(0, 0, 30, 10);
            comboBoxsifenhong.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxsifenhong.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxsifenhong.ScrollBarStyleInherited = false;
            comboBoxsifenhong.Size = new Size(70, 31);
            comboBoxsifenhong.SymbolSize = 24;
            comboBoxsifenhong.TabIndex = 45;
            comboBoxsifenhong.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxsifenhong.Watermark = "";
            comboBoxsifenhong.SelectedIndexChanged += comboBoxsifenhong_SelectedIndexChanged;
            // 
            // textboxerfenhong
            // 
            textboxerfenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxerfenhong.Location = new Point(298, 43);
            textboxerfenhong.Margin = new Padding(4, 5, 4, 5);
            textboxerfenhong.MinimumSize = new Size(1, 16);
            textboxerfenhong.Name = "textboxerfenhong";
            textboxerfenhong.Padding = new Padding(5);
            textboxerfenhong.RectColor = Color.FromArgb(255, 255, 192);
            textboxerfenhong.ShowText = false;
            textboxerfenhong.Size = new Size(77, 30);
            textboxerfenhong.TabIndex = 44;
            textboxerfenhong.Text = "0";
            textboxerfenhong.TextAlignment = ContentAlignment.MiddleCenter;
            textboxerfenhong.Watermark = "";
            textboxerfenhong.TextChanged += textBoxerfenhong_TextChanged;
            // 
            // checkBoxerfenhong
            // 
            checkBoxerfenhong.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxerfenhong.Location = new Point(20, 45);
            checkBoxerfenhong.Name = "checkBoxerfenhong";
            checkBoxerfenhong.Size = new Size(132, 23);
            checkBoxerfenhong.TabIndex = 43;
            checkBoxerfenhong.Text = "二分宏";
            checkBoxerfenhong.CheckedChanged += checkBoxerfenhong_CheckedChanged;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(228, 48);
            label2.Name = "label2";
            label2.Size = new Size(76, 23);
            label2.TabIndex = 42;
            label2.Text = "间隔：";
            // 
            // comboBoxerfenhong
            // 
            comboBoxerfenhong.DataSource = null;
            comboBoxerfenhong.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxerfenhong.DropDownWidth = 70;
            comboBoxerfenhong.FillColor = Color.White;
            comboBoxerfenhong.FillColorGradient = true;
            comboBoxerfenhong.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxerfenhong.ItemHeight = 40;
            comboBoxerfenhong.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxerfenhong.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxerfenhong.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxerfenhong.Location = new Point(155, 42);
            comboBoxerfenhong.Margin = new Padding(0);
            comboBoxerfenhong.MaxDropDownItems = 30;
            comboBoxerfenhong.MinimumSize = new Size(63, 0);
            comboBoxerfenhong.Name = "comboBoxerfenhong";
            comboBoxerfenhong.Padding = new Padding(0, 0, 30, 10);
            comboBoxerfenhong.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxerfenhong.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxerfenhong.ScrollBarStyleInherited = false;
            comboBoxerfenhong.Size = new Size(70, 31);
            comboBoxerfenhong.SymbolSize = 24;
            comboBoxerfenhong.TabIndex = 41;
            comboBoxerfenhong.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxerfenhong.Watermark = "";
            comboBoxerfenhong.SelectedIndexChanged += comboBoxerfenhong_SelectedIndexChanged;
            // 
            // uiGroupBox1
            // 
            uiGroupBox1.Controls.Add(comboBoxchanganhongmnj2);
            uiGroupBox1.Controls.Add(label13);
            uiGroupBox1.Controls.Add(comboBoxchanganhongmnj1);
            uiGroupBox1.Controls.Add(label14);
            uiGroupBox1.Controls.Add(textboxchanganhong2);
            uiGroupBox1.Controls.Add(checkBoxchanganhong2);
            uiGroupBox1.Controls.Add(label11);
            uiGroupBox1.Controls.Add(comboBoxchanganhong2);
            uiGroupBox1.Controls.Add(textboxchanganhong1);
            uiGroupBox1.Controls.Add(checkBoxchanganhong1);
            uiGroupBox1.Controls.Add(label12);
            uiGroupBox1.Controls.Add(comboBoxchanganhong1);
            uiGroupBox1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox1.Location = new Point(14, 318);
            uiGroupBox1.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox1.MinimumSize = new Size(1, 1);
            uiGroupBox1.Name = "uiGroupBox1";
            uiGroupBox1.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox1.Radius = 20;
            uiGroupBox1.RectColor = SystemColors.ActiveCaption;
            uiGroupBox1.Size = new Size(753, 134);
            uiGroupBox1.TabIndex = 42;
            uiGroupBox1.Text = "长按宏";
            uiGroupBox1.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // comboBoxchanganhongmnj2
            // 
            comboBoxchanganhongmnj2.DataSource = null;
            comboBoxchanganhongmnj2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxchanganhongmnj2.DropDownWidth = 70;
            comboBoxchanganhongmnj2.FillColor = Color.White;
            comboBoxchanganhongmnj2.FillColorGradient = true;
            comboBoxchanganhongmnj2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxchanganhongmnj2.ItemHeight = 40;
            comboBoxchanganhongmnj2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxchanganhongmnj2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxchanganhongmnj2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxchanganhongmnj2.Location = new Point(462, 85);
            comboBoxchanganhongmnj2.Margin = new Padding(0);
            comboBoxchanganhongmnj2.MaxDropDownItems = 30;
            comboBoxchanganhongmnj2.MinimumSize = new Size(63, 0);
            comboBoxchanganhongmnj2.Name = "comboBoxchanganhongmnj2";
            comboBoxchanganhongmnj2.Padding = new Padding(0, 0, 30, 10);
            comboBoxchanganhongmnj2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxchanganhongmnj2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxchanganhongmnj2.ScrollBarStyleInherited = false;
            comboBoxchanganhongmnj2.Size = new Size(70, 31);
            comboBoxchanganhongmnj2.SymbolSize = 24;
            comboBoxchanganhongmnj2.TabIndex = 47;
            comboBoxchanganhongmnj2.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxchanganhongmnj2.Watermark = "";
            comboBoxchanganhongmnj2.SelectedIndexChanged += comboBoxchanganhongmnj2_SelectedIndexChanged;
            // 
            // label13
            // 
            label13.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label13.Location = new Point(385, 91);
            label13.Name = "label13";
            label13.Size = new Size(76, 23);
            label13.TabIndex = 66;
            label13.Text = "模拟键";
            // 
            // comboBoxchanganhongmnj1
            // 
            comboBoxchanganhongmnj1.DataSource = null;
            comboBoxchanganhongmnj1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxchanganhongmnj1.DropDownWidth = 70;
            comboBoxchanganhongmnj1.FillColor = Color.White;
            comboBoxchanganhongmnj1.FillColorGradient = true;
            comboBoxchanganhongmnj1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxchanganhongmnj1.ItemHeight = 40;
            comboBoxchanganhongmnj1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxchanganhongmnj1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxchanganhongmnj1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxchanganhongmnj1.Location = new Point(462, 42);
            comboBoxchanganhongmnj1.Margin = new Padding(0);
            comboBoxchanganhongmnj1.MaxDropDownItems = 30;
            comboBoxchanganhongmnj1.MinimumSize = new Size(63, 0);
            comboBoxchanganhongmnj1.Name = "comboBoxchanganhongmnj1";
            comboBoxchanganhongmnj1.Padding = new Padding(0, 0, 30, 10);
            comboBoxchanganhongmnj1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxchanganhongmnj1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxchanganhongmnj1.ScrollBarStyleInherited = false;
            comboBoxchanganhongmnj1.Size = new Size(70, 31);
            comboBoxchanganhongmnj1.SymbolSize = 24;
            comboBoxchanganhongmnj1.TabIndex = 46;
            comboBoxchanganhongmnj1.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxchanganhongmnj1.Watermark = "";
            comboBoxchanganhongmnj1.SelectedIndexChanged += comboBoxchanganhongmnj1_SelectedIndexChanged;
            // 
            // label14
            // 
            label14.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label14.Location = new Point(385, 48);
            label14.Name = "label14";
            label14.Size = new Size(76, 23);
            label14.TabIndex = 65;
            label14.Text = "模拟键";
            // 
            // textboxchanganhong2
            // 
            textboxchanganhong2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxchanganhong2.Location = new Point(298, 86);
            textboxchanganhong2.Margin = new Padding(4, 5, 4, 5);
            textboxchanganhong2.MinimumSize = new Size(1, 16);
            textboxchanganhong2.Name = "textboxchanganhong2";
            textboxchanganhong2.Padding = new Padding(5);
            textboxchanganhong2.RectColor = Color.FromArgb(255, 255, 192);
            textboxchanganhong2.ShowText = false;
            textboxchanganhong2.Size = new Size(77, 30);
            textboxchanganhong2.TabIndex = 48;
            textboxchanganhong2.Text = "0";
            textboxchanganhong2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxchanganhong2.Watermark = "";
            textboxchanganhong2.TextChanged += textBoxchanganhong2_TextChanged;
            // 
            // checkBoxchanganhong2
            // 
            checkBoxchanganhong2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxchanganhong2.Location = new Point(20, 88);
            checkBoxchanganhong2.Name = "checkBoxchanganhong2";
            checkBoxchanganhong2.Size = new Size(132, 23);
            checkBoxchanganhong2.TabIndex = 47;
            checkBoxchanganhong2.Text = "长按宏2";
            checkBoxchanganhong2.CheckedChanged += checkBoxchanganhong2_CheckedChanged;
            // 
            // label11
            // 
            label11.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label11.Location = new Point(228, 91);
            label11.Name = "label11";
            label11.Size = new Size(76, 23);
            label11.TabIndex = 46;
            label11.Text = "间隔：";
            // 
            // comboBoxchanganhong2
            // 
            comboBoxchanganhong2.DataSource = null;
            comboBoxchanganhong2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxchanganhong2.DropDownWidth = 70;
            comboBoxchanganhong2.FillColor = Color.White;
            comboBoxchanganhong2.FillColorGradient = true;
            comboBoxchanganhong2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxchanganhong2.ItemHeight = 40;
            comboBoxchanganhong2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxchanganhong2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxchanganhong2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxchanganhong2.Location = new Point(155, 85);
            comboBoxchanganhong2.Margin = new Padding(0);
            comboBoxchanganhong2.MaxDropDownItems = 30;
            comboBoxchanganhong2.MinimumSize = new Size(63, 0);
            comboBoxchanganhong2.Name = "comboBoxchanganhong2";
            comboBoxchanganhong2.Padding = new Padding(0, 0, 30, 10);
            comboBoxchanganhong2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxchanganhong2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxchanganhong2.ScrollBarStyleInherited = false;
            comboBoxchanganhong2.Size = new Size(70, 31);
            comboBoxchanganhong2.SymbolSize = 24;
            comboBoxchanganhong2.TabIndex = 45;
            comboBoxchanganhong2.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxchanganhong2.Watermark = "";
            comboBoxchanganhong2.SelectedIndexChanged += comboBoxchanganhong2_SelectedIndexChanged;
            // 
            // textboxchanganhong1
            // 
            textboxchanganhong1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxchanganhong1.Location = new Point(298, 43);
            textboxchanganhong1.Margin = new Padding(4, 5, 4, 5);
            textboxchanganhong1.MinimumSize = new Size(1, 16);
            textboxchanganhong1.Name = "textboxchanganhong1";
            textboxchanganhong1.Padding = new Padding(5);
            textboxchanganhong1.RectColor = Color.FromArgb(255, 255, 192);
            textboxchanganhong1.ShowText = false;
            textboxchanganhong1.Size = new Size(77, 30);
            textboxchanganhong1.TabIndex = 44;
            textboxchanganhong1.Text = "0";
            textboxchanganhong1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxchanganhong1.Watermark = "";
            textboxchanganhong1.TextChanged += textBoxchanganhong1_TextChanged;
            // 
            // checkBoxchanganhong1
            // 
            checkBoxchanganhong1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxchanganhong1.Location = new Point(20, 45);
            checkBoxchanganhong1.Name = "checkBoxchanganhong1";
            checkBoxchanganhong1.Size = new Size(132, 23);
            checkBoxchanganhong1.TabIndex = 43;
            checkBoxchanganhong1.Text = "长按宏1";
            checkBoxchanganhong1.CheckedChanged += checkBoxchanganhong1_CheckedChanged;
            // 
            // label12
            // 
            label12.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label12.Location = new Point(228, 48);
            label12.Name = "label12";
            label12.Size = new Size(76, 23);
            label12.TabIndex = 42;
            label12.Text = "间隔：";
            // 
            // comboBoxchanganhong1
            // 
            comboBoxchanganhong1.DataSource = null;
            comboBoxchanganhong1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxchanganhong1.DropDownWidth = 70;
            comboBoxchanganhong1.FillColor = Color.White;
            comboBoxchanganhong1.FillColorGradient = true;
            comboBoxchanganhong1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxchanganhong1.ItemHeight = 40;
            comboBoxchanganhong1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxchanganhong1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxchanganhong1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxchanganhong1.Location = new Point(155, 42);
            comboBoxchanganhong1.Margin = new Padding(0);
            comboBoxchanganhong1.MaxDropDownItems = 30;
            comboBoxchanganhong1.MinimumSize = new Size(63, 0);
            comboBoxchanganhong1.Name = "comboBoxchanganhong1";
            comboBoxchanganhong1.Padding = new Padding(0, 0, 30, 10);
            comboBoxchanganhong1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxchanganhong1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxchanganhong1.ScrollBarStyleInherited = false;
            comboBoxchanganhong1.Size = new Size(70, 31);
            comboBoxchanganhong1.SymbolSize = 24;
            comboBoxchanganhong1.TabIndex = 41;
            comboBoxchanganhong1.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxchanganhong1.Watermark = "";
            comboBoxchanganhong1.SelectedIndexChanged += comboBoxchanganhong1_SelectedIndexChanged;
            // 
            // uiGroupBox2
            // 
            uiGroupBox2.Controls.Add(comboBoxlianjihongmnj2);
            uiGroupBox2.Controls.Add(label7);
            uiGroupBox2.Controls.Add(comboBoxlianjihongmnj1);
            uiGroupBox2.Controls.Add(label8);
            uiGroupBox2.Controls.Add(textboxlianjihong2);
            uiGroupBox2.Controls.Add(checkBoxlianjihong2);
            uiGroupBox2.Controls.Add(label9);
            uiGroupBox2.Controls.Add(comboBoxlianjihong2);
            uiGroupBox2.Controls.Add(textboxlianjihong1);
            uiGroupBox2.Controls.Add(checkBoxlianjihong1);
            uiGroupBox2.Controls.Add(label10);
            uiGroupBox2.Controls.Add(comboBoxlianjihong1);
            uiGroupBox2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox2.Location = new Point(14, 453);
            uiGroupBox2.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox2.MinimumSize = new Size(1, 1);
            uiGroupBox2.Name = "uiGroupBox2";
            uiGroupBox2.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox2.Radius = 20;
            uiGroupBox2.RectColor = SystemColors.ActiveCaption;
            uiGroupBox2.Size = new Size(753, 132);
            uiGroupBox2.TabIndex = 67;
            uiGroupBox2.Text = "连击宏";
            uiGroupBox2.TextAlignment = ContentAlignment.MiddleLeft;
            uiGroupBox2.Click += uiGroupBox2_Click;
            // 
            // comboBoxlianjihongmnj2
            // 
            comboBoxlianjihongmnj2.DataSource = null;
            comboBoxlianjihongmnj2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxlianjihongmnj2.DropDownWidth = 70;
            comboBoxlianjihongmnj2.FillColor = Color.White;
            comboBoxlianjihongmnj2.FillColorGradient = true;
            comboBoxlianjihongmnj2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxlianjihongmnj2.ItemHeight = 40;
            comboBoxlianjihongmnj2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxlianjihongmnj2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxlianjihongmnj2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxlianjihongmnj2.Location = new Point(462, 85);
            comboBoxlianjihongmnj2.Margin = new Padding(0);
            comboBoxlianjihongmnj2.MaxDropDownItems = 30;
            comboBoxlianjihongmnj2.MinimumSize = new Size(63, 0);
            comboBoxlianjihongmnj2.Name = "comboBoxlianjihongmnj2";
            comboBoxlianjihongmnj2.Padding = new Padding(0, 0, 30, 10);
            comboBoxlianjihongmnj2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxlianjihongmnj2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxlianjihongmnj2.ScrollBarStyleInherited = false;
            comboBoxlianjihongmnj2.Size = new Size(70, 31);
            comboBoxlianjihongmnj2.SymbolSize = 24;
            comboBoxlianjihongmnj2.TabIndex = 47;
            comboBoxlianjihongmnj2.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxlianjihongmnj2.Watermark = "";
            comboBoxlianjihongmnj2.SelectedIndexChanged += comboBoxlianjihongmnj2_SelectedIndexChanged;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(385, 91);
            label7.Name = "label7";
            label7.Size = new Size(76, 23);
            label7.TabIndex = 66;
            label7.Text = "模拟键";
            // 
            // comboBoxlianjihongmnj1
            // 
            comboBoxlianjihongmnj1.DataSource = null;
            comboBoxlianjihongmnj1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxlianjihongmnj1.DropDownWidth = 70;
            comboBoxlianjihongmnj1.FillColor = Color.White;
            comboBoxlianjihongmnj1.FillColorGradient = true;
            comboBoxlianjihongmnj1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxlianjihongmnj1.ItemHeight = 40;
            comboBoxlianjihongmnj1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxlianjihongmnj1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxlianjihongmnj1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxlianjihongmnj1.Location = new Point(462, 42);
            comboBoxlianjihongmnj1.Margin = new Padding(0);
            comboBoxlianjihongmnj1.MaxDropDownItems = 30;
            comboBoxlianjihongmnj1.MinimumSize = new Size(63, 0);
            comboBoxlianjihongmnj1.Name = "comboBoxlianjihongmnj1";
            comboBoxlianjihongmnj1.Padding = new Padding(0, 0, 30, 10);
            comboBoxlianjihongmnj1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxlianjihongmnj1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxlianjihongmnj1.ScrollBarStyleInherited = false;
            comboBoxlianjihongmnj1.Size = new Size(70, 31);
            comboBoxlianjihongmnj1.SymbolSize = 24;
            comboBoxlianjihongmnj1.TabIndex = 46;
            comboBoxlianjihongmnj1.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxlianjihongmnj1.Watermark = "";
            comboBoxlianjihongmnj1.SelectedIndexChanged += comboBoxlianjihongmnj1_SelectedIndexChanged;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(385, 48);
            label8.Name = "label8";
            label8.Size = new Size(76, 23);
            label8.TabIndex = 65;
            label8.Text = "模拟键";
            // 
            // textboxlianjihong2
            // 
            textboxlianjihong2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxlianjihong2.Location = new Point(298, 86);
            textboxlianjihong2.Margin = new Padding(4, 5, 4, 5);
            textboxlianjihong2.MinimumSize = new Size(1, 16);
            textboxlianjihong2.Name = "textboxlianjihong2";
            textboxlianjihong2.Padding = new Padding(5);
            textboxlianjihong2.RectColor = Color.FromArgb(255, 255, 192);
            textboxlianjihong2.ShowText = false;
            textboxlianjihong2.Size = new Size(77, 30);
            textboxlianjihong2.TabIndex = 48;
            textboxlianjihong2.Text = "0";
            textboxlianjihong2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxlianjihong2.Watermark = "";
            textboxlianjihong2.TextChanged += textBoxlianjihong2_TextChanged;
            // 
            // checkBoxlianjihong2
            // 
            checkBoxlianjihong2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxlianjihong2.Location = new Point(20, 88);
            checkBoxlianjihong2.Name = "checkBoxlianjihong2";
            checkBoxlianjihong2.Size = new Size(132, 23);
            checkBoxlianjihong2.TabIndex = 47;
            checkBoxlianjihong2.Text = "连击宏2";
            checkBoxlianjihong2.CheckedChanged += checkBoxlianjihong2_CheckedChanged;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(228, 91);
            label9.Name = "label9";
            label9.Size = new Size(76, 23);
            label9.TabIndex = 46;
            label9.Text = "次数：";
            // 
            // comboBoxlianjihong2
            // 
            comboBoxlianjihong2.DataSource = null;
            comboBoxlianjihong2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxlianjihong2.DropDownWidth = 70;
            comboBoxlianjihong2.FillColor = Color.White;
            comboBoxlianjihong2.FillColorGradient = true;
            comboBoxlianjihong2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxlianjihong2.ItemHeight = 40;
            comboBoxlianjihong2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxlianjihong2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxlianjihong2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxlianjihong2.Location = new Point(155, 85);
            comboBoxlianjihong2.Margin = new Padding(0);
            comboBoxlianjihong2.MaxDropDownItems = 30;
            comboBoxlianjihong2.MinimumSize = new Size(63, 0);
            comboBoxlianjihong2.Name = "comboBoxlianjihong2";
            comboBoxlianjihong2.Padding = new Padding(0, 0, 30, 10);
            comboBoxlianjihong2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxlianjihong2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxlianjihong2.ScrollBarStyleInherited = false;
            comboBoxlianjihong2.Size = new Size(70, 31);
            comboBoxlianjihong2.SymbolSize = 24;
            comboBoxlianjihong2.TabIndex = 45;
            comboBoxlianjihong2.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxlianjihong2.Watermark = "";
            comboBoxlianjihong2.SelectedIndexChanged += comboBoxlianjihong2_SelectedIndexChanged;
            // 
            // textboxlianjihong1
            // 
            textboxlianjihong1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxlianjihong1.Location = new Point(298, 43);
            textboxlianjihong1.Margin = new Padding(4, 5, 4, 5);
            textboxlianjihong1.MinimumSize = new Size(1, 16);
            textboxlianjihong1.Name = "textboxlianjihong1";
            textboxlianjihong1.Padding = new Padding(5);
            textboxlianjihong1.RectColor = Color.FromArgb(255, 255, 192);
            textboxlianjihong1.ShowText = false;
            textboxlianjihong1.Size = new Size(77, 30);
            textboxlianjihong1.TabIndex = 44;
            textboxlianjihong1.Text = "0";
            textboxlianjihong1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxlianjihong1.Watermark = "";
            textboxlianjihong1.TextChanged += textBoxlianjihong1_TextChanged;
            // 
            // checkBoxlianjihong1
            // 
            checkBoxlianjihong1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkBoxlianjihong1.Location = new Point(20, 45);
            checkBoxlianjihong1.Name = "checkBoxlianjihong1";
            checkBoxlianjihong1.Size = new Size(132, 23);
            checkBoxlianjihong1.TabIndex = 43;
            checkBoxlianjihong1.Text = "连击宏1";
            checkBoxlianjihong1.CheckedChanged += checkBoxlianjihong1_CheckedChanged;
            // 
            // label10
            // 
            label10.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label10.Location = new Point(228, 48);
            label10.Name = "label10";
            label10.Size = new Size(76, 23);
            label10.TabIndex = 42;
            label10.Text = "次数：";
            // 
            // comboBoxlianjihong1
            // 
            comboBoxlianjihong1.DataSource = null;
            comboBoxlianjihong1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxlianjihong1.DropDownWidth = 70;
            comboBoxlianjihong1.FillColor = Color.White;
            comboBoxlianjihong1.FillColorGradient = true;
            comboBoxlianjihong1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxlianjihong1.ItemHeight = 40;
            comboBoxlianjihong1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxlianjihong1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxlianjihong1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxlianjihong1.Location = new Point(155, 42);
            comboBoxlianjihong1.Margin = new Padding(0);
            comboBoxlianjihong1.MaxDropDownItems = 30;
            comboBoxlianjihong1.MinimumSize = new Size(63, 0);
            comboBoxlianjihong1.Name = "comboBoxlianjihong1";
            comboBoxlianjihong1.Padding = new Padding(0, 0, 30, 10);
            comboBoxlianjihong1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxlianjihong1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxlianjihong1.ScrollBarStyleInherited = false;
            comboBoxlianjihong1.Size = new Size(70, 31);
            comboBoxlianjihong1.SymbolSize = 24;
            comboBoxlianjihong1.TabIndex = 41;
            comboBoxlianjihong1.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxlianjihong1.Watermark = "";
            comboBoxlianjihong1.SelectedIndexChanged += comboBoxlianjihong1_SelectedIndexChanged;
            // 
            // honggongneng
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(uiGroupBox2);
            Controls.Add(uiGroupBox1);
            Controls.Add(Groupshiye);
            FormBorderStyle = FormBorderStyle.None;
            Name = "honggongneng";
            Text = "honggongneng";
            Load += honggongneng_Load;
            Groupshiye.ResumeLayout(false);
            uiGroupBox1.ResumeLayout(false);
            uiGroupBox2.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        public Sunny.UI.UIGroupBox Groupshiye;
        public Sunny.UI.UITextBox textboxshiliufenhong;
        public AntdUI.Checkbox checkBoxshiliufenhong;
        public AntdUI.Label label6;
        public Sunny.UI.UIComboBox comboBoxshiliufenhong;
        public Sunny.UI.UITextBox textboxbafenhong;
        public AntdUI.Checkbox checkBoxbafenhong;
        public AntdUI.Label label5;
        public Sunny.UI.UIComboBox comboBoxbafenhong;
        public Sunny.UI.UITextBox textboxgangganhong;
        public AntdUI.Checkbox checkBoxgangganhong;
        public AntdUI.Label label4;
        public Sunny.UI.UIComboBox comboBoxgangganhong;
        public Sunny.UI.UITextBox textboxyuandihong;
        public AntdUI.Checkbox checkBoxyuandihong;
        public AntdUI.Label label3;
        public Sunny.UI.UIComboBox comboBoxyuandihong;
        public Sunny.UI.UITextBox textboxsifenhong;
        public AntdUI.Checkbox checkBoxsifenhong;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboBoxsifenhong;
        public Sunny.UI.UITextBox textboxerfenhong;
        public AntdUI.Checkbox checkBoxerfenhong;
        public AntdUI.Label label2;
        public Sunny.UI.UIComboBox comboBoxerfenhong;
        public Sunny.UI.UIGroupBox uiGroupBox1;
        public Sunny.UI.UIComboBox comboBoxchanganhongmnj2;
        public AntdUI.Label label13;
        public Sunny.UI.UIComboBox comboBoxchanganhongmnj1;
        public AntdUI.Label label14;
        public Sunny.UI.UITextBox textboxchanganhong2;
        public AntdUI.Checkbox checkBoxchanganhong2;
        public AntdUI.Label label11;
        public Sunny.UI.UIComboBox comboBoxchanganhong2;
        public Sunny.UI.UITextBox textboxchanganhong1;
        public AntdUI.Checkbox checkBoxchanganhong1;
        public AntdUI.Label label12;
        public Sunny.UI.UIComboBox comboBoxchanganhong1;
        public Sunny.UI.UIGroupBox uiGroupBox2;
        public Sunny.UI.UIComboBox comboBoxlianjihongmnj2;
        public AntdUI.Label label7;
        public Sunny.UI.UIComboBox comboBoxlianjihongmnj1;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxlianjihong2;
        public AntdUI.Checkbox checkBoxlianjihong2;
        public AntdUI.Label label9;
        public Sunny.UI.UIComboBox comboBoxlianjihong2;
        public Sunny.UI.UITextBox textboxlianjihong1;
        public AntdUI.Checkbox checkBoxlianjihong1;
        public AntdUI.Label label10;
        public Sunny.UI.UIComboBox comboBoxlianjihong1;
    }
}