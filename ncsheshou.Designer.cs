﻿namespace ANYE_Balls
{
    partial class ncsheshou
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxncssjd4 = new Sunny.UI.UITextBox();
            label8 = new AntdUI.Label();
            textboxncssjcfd4 = new Sunny.UI.UITextBox();
            label9 = new AntdUI.Label();
            textboxncssjd3 = new Sunny.UI.UITextBox();
            label5 = new AntdUI.Label();
            textboxncssjcfd3 = new Sunny.UI.UITextBox();
            label6 = new AntdUI.Label();
            textboxncssjd2 = new Sunny.UI.UITextBox();
            label3 = new AntdUI.Label();
            textboxncssjcfd2 = new Sunny.UI.UITextBox();
            label4 = new AntdUI.Label();
            textboxncssjd1 = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            datagridviewncss = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxncsstq = new AntdUI.Checkbox();
            textboxncssjcfd1 = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxncsheshou = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxncsheshou = new AntdUI.Checkbox();
            buttonncsscz = new AntdUI.Button();
            button1 = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewncss).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxncssjd4
            // 
            textboxncssjd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjd4.Location = new Point(125, 469);
            textboxncssjd4.Margin = new Padding(4, 5, 4, 5);
            textboxncssjd4.MinimumSize = new Size(1, 16);
            textboxncssjd4.Name = "textboxncssjd4";
            textboxncssjd4.Padding = new Padding(5);
            textboxncssjd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjd4.ShowText = false;
            textboxncssjd4.Size = new Size(70, 30);
            textboxncssjd4.TabIndex = 164;
            textboxncssjd4.Text = "0";
            textboxncssjd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjd4.Watermark = "";
            textboxncssjd4.TextChanged += textboxncssjd4_TextChanged;
            textboxncssjd4.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label8.Location = new Point(60, 473);
            label8.Name = "label8";
            label8.Size = new Size(68, 23);
            label8.TabIndex = 163;
            label8.Text = "角度4";
            // 
            // textboxncssjcfd4
            // 
            textboxncssjcfd4.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjcfd4.Location = new Point(125, 435);
            textboxncssjcfd4.Margin = new Padding(4, 5, 4, 5);
            textboxncssjcfd4.MinimumSize = new Size(1, 16);
            textboxncssjcfd4.Name = "textboxncssjcfd4";
            textboxncssjcfd4.Padding = new Padding(5);
            textboxncssjcfd4.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjcfd4.ShowText = false;
            textboxncssjcfd4.Size = new Size(70, 30);
            textboxncssjcfd4.TabIndex = 162;
            textboxncssjcfd4.Text = "0";
            textboxncssjcfd4.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjcfd4.Watermark = "";
            textboxncssjcfd4.TextChanged += textboxncssjcfd4_TextChanged;
            textboxncssjcfd4.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.Location = new Point(19, 439);
            label9.Name = "label9";
            label9.Size = new Size(109, 23);
            label9.TabIndex = 161;
            label9.Text = "交叉幅度4";
            // 
            // textboxncssjd3
            // 
            textboxncssjd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjd3.Location = new Point(125, 401);
            textboxncssjd3.Margin = new Padding(4, 5, 4, 5);
            textboxncssjd3.MinimumSize = new Size(1, 16);
            textboxncssjd3.Name = "textboxncssjd3";
            textboxncssjd3.Padding = new Padding(5);
            textboxncssjd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjd3.ShowText = false;
            textboxncssjd3.Size = new Size(70, 30);
            textboxncssjd3.TabIndex = 160;
            textboxncssjd3.Text = "0";
            textboxncssjd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjd3.Watermark = "";
            textboxncssjd3.TextChanged += textboxncssjd3_TextChanged;
            textboxncssjd3.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(60, 405);
            label5.Name = "label5";
            label5.Size = new Size(68, 23);
            label5.TabIndex = 159;
            label5.Text = "角度3";
            // 
            // textboxncssjcfd3
            // 
            textboxncssjcfd3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjcfd3.Location = new Point(125, 367);
            textboxncssjcfd3.Margin = new Padding(4, 5, 4, 5);
            textboxncssjcfd3.MinimumSize = new Size(1, 16);
            textboxncssjcfd3.Name = "textboxncssjcfd3";
            textboxncssjcfd3.Padding = new Padding(5);
            textboxncssjcfd3.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjcfd3.ShowText = false;
            textboxncssjcfd3.Size = new Size(70, 30);
            textboxncssjcfd3.TabIndex = 158;
            textboxncssjcfd3.Text = "0";
            textboxncssjcfd3.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjcfd3.Watermark = "";
            textboxncssjcfd3.TextChanged += textboxncssjcfd3_TextChanged;
            textboxncssjcfd3.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(19, 371);
            label6.Name = "label6";
            label6.Size = new Size(109, 23);
            label6.TabIndex = 157;
            label6.Text = "交叉幅度3";
            // 
            // textboxncssjd2
            // 
            textboxncssjd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjd2.Location = new Point(125, 333);
            textboxncssjd2.Margin = new Padding(4, 5, 4, 5);
            textboxncssjd2.MinimumSize = new Size(1, 16);
            textboxncssjd2.Name = "textboxncssjd2";
            textboxncssjd2.Padding = new Padding(5);
            textboxncssjd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjd2.ShowText = false;
            textboxncssjd2.Size = new Size(70, 30);
            textboxncssjd2.TabIndex = 156;
            textboxncssjd2.Text = "0";
            textboxncssjd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjd2.Watermark = "";
            textboxncssjd2.TextChanged += textboxncssjd2_TextChanged;
            textboxncssjd2.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(60, 337);
            label3.Name = "label3";
            label3.Size = new Size(68, 23);
            label3.TabIndex = 155;
            label3.Text = "角度2";
            // 
            // textboxncssjcfd2
            // 
            textboxncssjcfd2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjcfd2.Location = new Point(125, 299);
            textboxncssjcfd2.Margin = new Padding(4, 5, 4, 5);
            textboxncssjcfd2.MinimumSize = new Size(1, 16);
            textboxncssjcfd2.Name = "textboxncssjcfd2";
            textboxncssjcfd2.Padding = new Padding(5);
            textboxncssjcfd2.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjcfd2.ShowText = false;
            textboxncssjcfd2.Size = new Size(70, 30);
            textboxncssjcfd2.TabIndex = 154;
            textboxncssjcfd2.Text = "0";
            textboxncssjcfd2.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjcfd2.Watermark = "";
            textboxncssjcfd2.TextChanged += textboxncssjcfd2_TextChanged;
            textboxncssjcfd2.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(19, 303);
            label4.Name = "label4";
            label4.Size = new Size(109, 23);
            label4.TabIndex = 153;
            label4.Text = "交叉幅度2";
            // 
            // textboxncssjd1
            // 
            textboxncssjd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjd1.Location = new Point(125, 265);
            textboxncssjd1.Margin = new Padding(4, 5, 4, 5);
            textboxncssjd1.MinimumSize = new Size(1, 16);
            textboxncssjd1.Name = "textboxncssjd1";
            textboxncssjd1.Padding = new Padding(5);
            textboxncssjd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjd1.ShowText = false;
            textboxncssjd1.Size = new Size(70, 30);
            textboxncssjd1.TabIndex = 152;
            textboxncssjd1.Text = "0";
            textboxncssjd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjd1.Watermark = "";
            textboxncssjd1.TextChanged += textboxncssjd1_TextChanged;
            textboxncssjd1.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(60, 269);
            label2.Name = "label2";
            label2.Size = new Size(68, 23);
            label2.TabIndex = 151;
            label2.Text = "角度1";
            // 
            // datagridviewncss
            // 
            datagridviewncss.AllowUserToAddRows = false;
            datagridviewncss.AllowUserToDeleteRows = false;
            datagridviewncss.AllowUserToResizeColumns = false;
            datagridviewncss.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewncss.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewncss.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewncss.BorderStyle = BorderStyle.Fixed3D;
            datagridviewncss.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewncss.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewncss.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewncss.ColumnHeadersHeight = 40;
            datagridviewncss.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewncss.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewncss.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewncss.EnableHeadersVisualStyles = false;
            datagridviewncss.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewncss.GridColor = Color.AliceBlue;
            datagridviewncss.Location = new Point(309, 184);
            datagridviewncss.Name = "datagridviewncss";
            datagridviewncss.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewncss.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewncss.RowHeadersVisible = false;
            datagridviewncss.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewncss.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewncss.RowTemplate.Height = 25;
            datagridviewncss.ScrollBars = ScrollBars.None;
            datagridviewncss.SelectedIndex = -1;
            datagridviewncss.Size = new Size(294, 322);
            datagridviewncss.TabIndex = 150;
            datagridviewncss.CellValidating += datagridviewncss_CellValidating;
            datagridviewncss.CellValueChanged += datagridviewncss_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxncsstq
            // 
            checkboxncsstq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsstq.Location = new Point(607, 186);
            checkboxncsstq.Name = "checkboxncsstq";
            checkboxncsstq.Size = new Size(131, 23);
            checkboxncsstq.TabIndex = 149;
            checkboxncsstq.Text = "自动吐球";
            checkboxncsstq.CheckedChanged += checkboxncsstq_CheckedChanged;
            // 
            // textboxncssjcfd1
            // 
            textboxncssjcfd1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncssjcfd1.Location = new Point(125, 231);
            textboxncssjcfd1.Margin = new Padding(4, 5, 4, 5);
            textboxncssjcfd1.MinimumSize = new Size(1, 16);
            textboxncssjcfd1.Name = "textboxncssjcfd1";
            textboxncssjcfd1.Padding = new Padding(5);
            textboxncssjcfd1.RectColor = Color.FromArgb(255, 255, 192);
            textboxncssjcfd1.ShowText = false;
            textboxncssjcfd1.Size = new Size(70, 30);
            textboxncssjcfd1.TabIndex = 148;
            textboxncssjcfd1.Text = "0";
            textboxncssjcfd1.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncssjcfd1.Watermark = "";
            textboxncssjcfd1.TextChanged += textboxncssjcfd1_TextChanged;
            textboxncssjcfd1.KeyPress += textboxncssjcfd1_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(19, 235);
            label1.Name = "label1";
            label1.Size = new Size(109, 23);
            label1.TabIndex = 147;
            label1.Text = "交叉幅度1";
            // 
            // comboboxncsheshou
            // 
            comboboxncsheshou.DataSource = null;
            comboboxncsheshou.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxncsheshou.DropDownWidth = 70;
            comboboxncsheshou.FillColor = Color.White;
            comboboxncsheshou.FillColorGradient = true;
            comboboxncsheshou.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxncsheshou.ItemHeight = 40;
            comboboxncsheshou.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxncsheshou.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxncsheshou.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxncsheshou.Location = new Point(125, 180);
            comboboxncsheshou.Margin = new Padding(0);
            comboboxncsheshou.MaxDropDownItems = 30;
            comboboxncsheshou.MinimumSize = new Size(63, 0);
            comboboxncsheshou.Name = "comboboxncsheshou";
            comboboxncsheshou.Padding = new Padding(0, 0, 30, 10);
            comboboxncsheshou.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxncsheshou.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxncsheshou.ScrollBarStyleInherited = false;
            comboboxncsheshou.Size = new Size(70, 31);
            comboboxncsheshou.SymbolSize = 24;
            comboboxncsheshou.TabIndex = 146;
            comboboxncsheshou.Text = "A";
            comboboxncsheshou.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxncsheshou.Watermark = "";
            comboboxncsheshou.SelectedIndexChanged += comboboxncsheshou_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 143;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 145;
            label7.Text = "快捷键";
            // 
            // checkboxncsheshou
            // 
            checkboxncsheshou.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsheshou.Location = new Point(208, 184);
            checkboxncsheshou.Name = "checkboxncsheshou";
            checkboxncsheshou.Size = new Size(95, 23);
            checkboxncsheshou.TabIndex = 144;
            checkboxncsheshou.Text = "启用";
            checkboxncsheshou.CheckedChanged += checkboxncsheshou_CheckedChanged;
            // 
            // buttonncsscz
            // 
            buttonncsscz.BackHover = Color.Aquamarine;
            buttonncsscz.DefaultBack = Color.Azure;
            buttonncsscz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonncsscz.Location = new Point(208, 231);
            buttonncsscz.Name = "buttonncsscz";
            buttonncsscz.Size = new Size(95, 64);
            buttonncsscz.TabIndex = 165;
            buttonncsscz.Text = "重置";
            buttonncsscz.Click += buttonncsscz_Click;
            // 
            // button1
            // 
            button1.BackHover = Color.Aquamarine;
            button1.DefaultBack = Color.Azure;
            button1.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            button1.Location = new Point(208, 301);
            button1.Name = "button1";
            button1.Size = new Size(95, 93);
            button1.TabIndex = 166;
            button1.Text = "旋转\r\n拐弯";
            button1.Click += button1_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 55;
            label19.Text = "遥杆方向决定蛇手最终合球方向，鼠标相对于遥杆哪边就往哪边拐";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // ncsheshou
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(button1);
            Controls.Add(buttonncsscz);
            Controls.Add(textboxncssjd4);
            Controls.Add(label8);
            Controls.Add(textboxncssjcfd4);
            Controls.Add(label9);
            Controls.Add(textboxncssjd3);
            Controls.Add(label5);
            Controls.Add(textboxncssjcfd3);
            Controls.Add(label6);
            Controls.Add(textboxncssjd2);
            Controls.Add(label3);
            Controls.Add(textboxncssjcfd2);
            Controls.Add(label4);
            Controls.Add(textboxncssjd1);
            Controls.Add(label2);
            Controls.Add(datagridviewncss);
            Controls.Add(checkboxncsstq);
            Controls.Add(textboxncssjcfd1);
            Controls.Add(label1);
            Controls.Add(comboboxncsheshou);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxncsheshou);
            FormBorderStyle = FormBorderStyle.None;
            Name = "ncsheshou";
            Text = "ncsheshou";
            Load += ncsheshou_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewncss).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewncss;
        public Sunny.UI.UITextBox textboxncssjd4;
        public AntdUI.Label label8;
        public Sunny.UI.UITextBox textboxncssjcfd4;
        public AntdUI.Label label9;
        public Sunny.UI.UITextBox textboxncssjd3;
        public AntdUI.Label label5;
        public Sunny.UI.UITextBox textboxncssjcfd3;
        public AntdUI.Label label6;
        public Sunny.UI.UITextBox textboxncssjd2;
        public AntdUI.Label label3;
        public Sunny.UI.UITextBox textboxncssjcfd2;
        public AntdUI.Label label4;
        public Sunny.UI.UITextBox textboxncssjd1;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxncsstq;
        public Sunny.UI.UITextBox textboxncssjcfd1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxncsheshou;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxncsheshou;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonncsscz;
        public AntdUI.Button button1;
        public AntdUI.Label label19;
    }
}