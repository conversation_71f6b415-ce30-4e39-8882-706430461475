﻿namespace ANYE_Balls
{
    partial class fuzhugongneng
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label9 = new AntdUI.Label();
            comboBoxmnj3 = new Sunny.UI.UIComboBox();
            label5 = new AntdUI.Label();
            comboBoxtb3 = new Sunny.UI.UIComboBox();
            checkboxtb3 = new AntdUI.Checkbox();
            label6 = new AntdUI.Label();
            comboBoxmnj2 = new Sunny.UI.UIComboBox();
            label3 = new AntdUI.Label();
            comboBoxtb2 = new Sunny.UI.UIComboBox();
            checkboxtb2 = new AntdUI.Checkbox();
            label4 = new AntdUI.Label();
            comboBoxmnj1 = new Sunny.UI.UIComboBox();
            label2 = new AntdUI.Label();
            comboBoxtb1 = new Sunny.UI.UIComboBox();
            checkboxtb1 = new AntdUI.Checkbox();
            label1 = new AntdUI.Label();
            uiGroupBox1 = new Sunny.UI.UIGroupBox();
            label11 = new AntdUI.Label();
            checkboxygtm = new AntdUI.Checkbox();
            label10 = new AntdUI.Label();
            label8 = new AntdUI.Label();
            label19 = new AntdUI.Label();
            checkboxkongqiu = new AntdUI.Checkbox();
            textboxkongqiu = new Sunny.UI.UITextBox();
            label7 = new AntdUI.Label();
            comboBoxtsuoqiu = new Sunny.UI.UIComboBox();
            checkboxsuoqiu = new AntdUI.Checkbox();
            label12 = new AntdUI.Label();
            label13 = new AntdUI.Label();
            Grouptongbu.SuspendLayout();
            uiGroupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label9);
            Grouptongbu.Controls.Add(comboBoxmnj3);
            Grouptongbu.Controls.Add(label5);
            Grouptongbu.Controls.Add(comboBoxtb3);
            Grouptongbu.Controls.Add(checkboxtb3);
            Grouptongbu.Controls.Add(label6);
            Grouptongbu.Controls.Add(comboBoxmnj2);
            Grouptongbu.Controls.Add(label3);
            Grouptongbu.Controls.Add(comboBoxtb2);
            Grouptongbu.Controls.Add(checkboxtb2);
            Grouptongbu.Controls.Add(label4);
            Grouptongbu.Controls.Add(comboBoxmnj1);
            Grouptongbu.Controls.Add(label2);
            Grouptongbu.Controls.Add(comboBoxtb1);
            Grouptongbu.Controls.Add(checkboxtb1);
            Grouptongbu.Controls.Add(label1);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 1;
            Grouptongbu.Text = "同步功能";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label9
            // 
            label9.Font = new Font("Microsoft YaHei UI", 9.75F, FontStyle.Regular, GraphicsUnit.Point);
            label9.ForeColor = Color.Green;
            label9.Location = new Point(490, 39);
            label9.Name = "label9";
            label9.Size = new Size(247, 116);
            label9.TabIndex = 55;
            label9.Text = "启用之后，当按下指定键时，将会开始检测自身体积变化，若体积变化超过一定幅度，则执行模拟键。同步隐弹大炮-将模拟键设置成一键16分键。同步隐弹合球-将模拟键设置成对应自动合球键。注：1.队友必须在体内跳出。2.队友跳之前不能吐球。";
            label9.TextAlign = ContentAlignment.TopLeft;
            // 
            // comboBoxmnj3
            // 
            comboBoxmnj3.DataSource = null;
            comboBoxmnj3.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxmnj3.DropDownWidth = 70;
            comboBoxmnj3.FillColor = Color.White;
            comboBoxmnj3.FillColorGradient = true;
            comboBoxmnj3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxmnj3.ItemHeight = 40;
            comboBoxmnj3.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxmnj3.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxmnj3.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxmnj3.Location = new Point(319, 120);
            comboBoxmnj3.Margin = new Padding(0);
            comboBoxmnj3.MaxDropDownItems = 30;
            comboBoxmnj3.MinimumSize = new Size(63, 0);
            comboBoxmnj3.Name = "comboBoxmnj3";
            comboBoxmnj3.Padding = new Padding(0, 0, 30, 10);
            comboBoxmnj3.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxmnj3.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxmnj3.ScrollBarStyleInherited = false;
            comboBoxmnj3.Size = new Size(70, 31);
            comboBoxmnj3.SymbolSize = 24;
            comboBoxmnj3.TabIndex = 25;
            comboBoxmnj3.Text = "A";
            comboBoxmnj3.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxmnj3.Watermark = "";
            comboBoxmnj3.SelectedIndexChanged += comboBoxmnj3_SelectedIndexChanged;
            // 
            // label5
            // 
            label5.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label5.Location = new Point(232, 124);
            label5.Name = "label5";
            label5.Size = new Size(89, 23);
            label5.TabIndex = 26;
            label5.Text = "模拟键3";
            // 
            // comboBoxtb3
            // 
            comboBoxtb3.DataSource = null;
            comboBoxtb3.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxtb3.DropDownWidth = 70;
            comboBoxtb3.FillColor = Color.White;
            comboBoxtb3.FillColorGradient = true;
            comboBoxtb3.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxtb3.ItemHeight = 40;
            comboBoxtb3.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxtb3.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxtb3.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxtb3.Location = new Point(147, 120);
            comboBoxtb3.Margin = new Padding(0);
            comboBoxtb3.MaxDropDownItems = 30;
            comboBoxtb3.MinimumSize = new Size(63, 0);
            comboBoxtb3.Name = "comboBoxtb3";
            comboBoxtb3.Padding = new Padding(0, 0, 30, 10);
            comboBoxtb3.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxtb3.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxtb3.ScrollBarStyleInherited = false;
            comboBoxtb3.Size = new Size(70, 31);
            comboBoxtb3.SymbolSize = 24;
            comboBoxtb3.TabIndex = 24;
            comboBoxtb3.Text = "A";
            comboBoxtb3.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxtb3.Watermark = "";
            comboBoxtb3.SelectedIndexChanged += comboBoxtb3_SelectedIndexChanged;
            // 
            // checkboxtb3
            // 
            checkboxtb3.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxtb3.Location = new Point(392, 124);
            checkboxtb3.Name = "checkboxtb3";
            checkboxtb3.Size = new Size(135, 23);
            checkboxtb3.TabIndex = 23;
            checkboxtb3.Text = "启用";
            checkboxtb3.CheckedChanged += checkboxtb3_CheckedChanged;
            // 
            // label6
            // 
            label6.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label6.Location = new Point(14, 124);
            label6.Name = "label6";
            label6.Size = new Size(130, 23);
            label6.TabIndex = 22;
            label6.Text = "同步触发键3";
            // 
            // comboBoxmnj2
            // 
            comboBoxmnj2.DataSource = null;
            comboBoxmnj2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxmnj2.DropDownWidth = 70;
            comboBoxmnj2.FillColor = Color.White;
            comboBoxmnj2.FillColorGradient = true;
            comboBoxmnj2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxmnj2.ItemHeight = 40;
            comboBoxmnj2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxmnj2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxmnj2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxmnj2.Location = new Point(319, 80);
            comboBoxmnj2.Margin = new Padding(0);
            comboBoxmnj2.MaxDropDownItems = 30;
            comboBoxmnj2.MinimumSize = new Size(63, 0);
            comboBoxmnj2.Name = "comboBoxmnj2";
            comboBoxmnj2.Padding = new Padding(0, 0, 30, 10);
            comboBoxmnj2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxmnj2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxmnj2.ScrollBarStyleInherited = false;
            comboBoxmnj2.Size = new Size(70, 31);
            comboBoxmnj2.SymbolSize = 24;
            comboBoxmnj2.TabIndex = 20;
            comboBoxmnj2.Text = "A";
            comboBoxmnj2.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxmnj2.Watermark = "";
            comboBoxmnj2.SelectedIndexChanged += comboBoxmnj2_SelectedIndexChanged;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label3.Location = new Point(232, 84);
            label3.Name = "label3";
            label3.Size = new Size(89, 23);
            label3.TabIndex = 21;
            label3.Text = "模拟键2";
            // 
            // comboBoxtb2
            // 
            comboBoxtb2.DataSource = null;
            comboBoxtb2.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxtb2.DropDownWidth = 70;
            comboBoxtb2.FillColor = Color.White;
            comboBoxtb2.FillColorGradient = true;
            comboBoxtb2.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxtb2.ItemHeight = 40;
            comboBoxtb2.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxtb2.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxtb2.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxtb2.Location = new Point(147, 80);
            comboBoxtb2.Margin = new Padding(0);
            comboBoxtb2.MaxDropDownItems = 30;
            comboBoxtb2.MinimumSize = new Size(63, 0);
            comboBoxtb2.Name = "comboBoxtb2";
            comboBoxtb2.Padding = new Padding(0, 0, 30, 10);
            comboBoxtb2.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxtb2.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxtb2.ScrollBarStyleInherited = false;
            comboBoxtb2.Size = new Size(70, 31);
            comboBoxtb2.SymbolSize = 24;
            comboBoxtb2.TabIndex = 19;
            comboBoxtb2.Text = "A";
            comboBoxtb2.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxtb2.Watermark = "";
            comboBoxtb2.SelectedIndexChanged += comboBoxtb2_SelectedIndexChanged;
            // 
            // checkboxtb2
            // 
            checkboxtb2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxtb2.Location = new Point(392, 84);
            checkboxtb2.Name = "checkboxtb2";
            checkboxtb2.Size = new Size(135, 23);
            checkboxtb2.TabIndex = 18;
            checkboxtb2.Text = "启用";
            checkboxtb2.CheckedChanged += checkboxtb2_CheckedChanged;
            // 
            // label4
            // 
            label4.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label4.Location = new Point(14, 84);
            label4.Name = "label4";
            label4.Size = new Size(130, 23);
            label4.TabIndex = 17;
            label4.Text = "同步触发键2";
            // 
            // comboBoxmnj1
            // 
            comboBoxmnj1.DataSource = null;
            comboBoxmnj1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxmnj1.DropDownWidth = 70;
            comboBoxmnj1.FillColor = Color.White;
            comboBoxmnj1.FillColorGradient = true;
            comboBoxmnj1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxmnj1.ItemHeight = 40;
            comboBoxmnj1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxmnj1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxmnj1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxmnj1.Location = new Point(319, 40);
            comboBoxmnj1.Margin = new Padding(0);
            comboBoxmnj1.MaxDropDownItems = 30;
            comboBoxmnj1.MinimumSize = new Size(63, 0);
            comboBoxmnj1.Name = "comboBoxmnj1";
            comboBoxmnj1.Padding = new Padding(0, 0, 30, 10);
            comboBoxmnj1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxmnj1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxmnj1.ScrollBarStyleInherited = false;
            comboBoxmnj1.Size = new Size(70, 31);
            comboBoxmnj1.SymbolSize = 24;
            comboBoxmnj1.TabIndex = 16;
            comboBoxmnj1.Text = "A";
            comboBoxmnj1.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxmnj1.Watermark = "";
            comboBoxmnj1.SelectedIndexChanged += comboBoxmnj1_SelectedIndexChanged;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(232, 44);
            label2.Name = "label2";
            label2.Size = new Size(89, 23);
            label2.TabIndex = 16;
            label2.Text = "模拟键1";
            // 
            // comboBoxtb1
            // 
            comboBoxtb1.DataSource = null;
            comboBoxtb1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxtb1.DropDownWidth = 70;
            comboBoxtb1.FillColor = Color.White;
            comboBoxtb1.FillColorGradient = true;
            comboBoxtb1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxtb1.ItemHeight = 40;
            comboBoxtb1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxtb1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxtb1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxtb1.Location = new Point(147, 40);
            comboBoxtb1.Margin = new Padding(0);
            comboBoxtb1.MaxDropDownItems = 30;
            comboBoxtb1.MinimumSize = new Size(63, 0);
            comboBoxtb1.Name = "comboBoxtb1";
            comboBoxtb1.Padding = new Padding(0, 0, 30, 10);
            comboBoxtb1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxtb1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxtb1.ScrollBarStyleInherited = false;
            comboBoxtb1.Size = new Size(70, 31);
            comboBoxtb1.SymbolSize = 24;
            comboBoxtb1.TabIndex = 15;
            comboBoxtb1.Text = "A";
            comboBoxtb1.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxtb1.Watermark = "";
            comboBoxtb1.SelectedIndexChanged += comboBoxtb1_SelectedIndexChanged;
            // 
            // checkboxtb1
            // 
            checkboxtb1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxtb1.Location = new Point(392, 44);
            checkboxtb1.Name = "checkboxtb1";
            checkboxtb1.Size = new Size(135, 23);
            checkboxtb1.TabIndex = 12;
            checkboxtb1.Text = "启用";
            checkboxtb1.CheckedChanged += checkboxtb1_CheckedChanged;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(14, 44);
            label1.Name = "label1";
            label1.Size = new Size(130, 23);
            label1.TabIndex = 0;
            label1.Text = "同步触发键1";
            // 
            // uiGroupBox1
            // 
            uiGroupBox1.Controls.Add(label13);
            uiGroupBox1.Controls.Add(label11);
            uiGroupBox1.Controls.Add(checkboxygtm);
            uiGroupBox1.Controls.Add(label10);
            uiGroupBox1.Controls.Add(label8);
            uiGroupBox1.Controls.Add(label19);
            uiGroupBox1.Controls.Add(checkboxkongqiu);
            uiGroupBox1.Controls.Add(textboxkongqiu);
            uiGroupBox1.Controls.Add(label7);
            uiGroupBox1.Controls.Add(comboBoxtsuoqiu);
            uiGroupBox1.Controls.Add(checkboxsuoqiu);
            uiGroupBox1.Controls.Add(label12);
            uiGroupBox1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox1.Location = new Point(14, 170);
            uiGroupBox1.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox1.MinimumSize = new Size(1, 1);
            uiGroupBox1.Name = "uiGroupBox1";
            uiGroupBox1.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox1.Radius = 20;
            uiGroupBox1.RectColor = SystemColors.ActiveCaption;
            uiGroupBox1.Size = new Size(753, 174);
            uiGroupBox1.TabIndex = 27;
            uiGroupBox1.Text = "辅助功能";
            uiGroupBox1.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label11
            // 
            label11.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label11.ForeColor = Color.Green;
            label11.Location = new Point(232, 115);
            label11.Name = "label11";
            label11.Size = new Size(494, 21);
            label11.TabIndex = 58;
            label11.Text = "启用之后，遥杆将完全透明，低配电脑若开启后造成卡顿则不建议开启";
            label11.TextAlign = ContentAlignment.TopLeft;
            // 
            // checkboxygtm
            // 
            checkboxygtm.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxygtm.Location = new Point(127, 113);
            checkboxygtm.Name = "checkboxygtm";
            checkboxygtm.Size = new Size(90, 23);
            checkboxygtm.TabIndex = 57;
            checkboxygtm.Text = "启用";
            checkboxygtm.CheckedChanged += checkbox1_CheckedChanged;
            // 
            // label10
            // 
            label10.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label10.Location = new Point(14, 113);
            label10.Name = "label10";
            label10.Size = new Size(120, 23);
            label10.TabIndex = 56;
            label10.Text = "遥杆全透明";
            // 
            // label8
            // 
            label8.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label8.ForeColor = Color.Green;
            label8.Location = new Point(377, 81);
            label8.Name = "label8";
            label8.Size = new Size(321, 21);
            label8.TabIndex = 55;
            label8.Text = "启用之后，鼠标右键拉遥杆锁定在指定范围内";
            label8.TextAlign = ContentAlignment.TopLeft;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(348, 44);
            label19.Name = "label19";
            label19.Size = new Size(389, 22);
            label19.TabIndex = 54;
            label19.Text = "启用之后，当按下此键时，自动往鼠标位置吐球";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // checkboxkongqiu
            // 
            checkboxkongqiu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxkongqiu.Location = new Point(279, 80);
            checkboxkongqiu.Name = "checkboxkongqiu";
            checkboxkongqiu.Size = new Size(135, 23);
            checkboxkongqiu.TabIndex = 18;
            checkboxkongqiu.Text = "启用";
            checkboxkongqiu.CheckedChanged += checkboxkongqiu_CheckedChanged;
            // 
            // textboxkongqiu
            // 
            textboxkongqiu.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxkongqiu.Location = new Point(195, 75);
            textboxkongqiu.Margin = new Padding(4, 5, 4, 5);
            textboxkongqiu.MaxLength = 3;
            textboxkongqiu.MinimumSize = new Size(1, 16);
            textboxkongqiu.Name = "textboxkongqiu";
            textboxkongqiu.Padding = new Padding(5);
            textboxkongqiu.RectColor = Color.FromArgb(255, 255, 192);
            textboxkongqiu.ShowText = false;
            textboxkongqiu.Size = new Size(77, 30);
            textboxkongqiu.TabIndex = 17;
            textboxkongqiu.Text = "0";
            textboxkongqiu.TextAlignment = ContentAlignment.MiddleCenter;
            textboxkongqiu.Watermark = "";
            textboxkongqiu.TextChanged += textboxkongqiu_TextChanged;
            textboxkongqiu.KeyPress += textboxkongqiu_KeyPress;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(14, 79);
            label7.Name = "label7";
            label7.Size = new Size(184, 23);
            label7.TabIndex = 16;
            label7.Text = "鼠标右键交叉控球";
            // 
            // comboBoxtsuoqiu
            // 
            comboBoxtsuoqiu.DataSource = null;
            comboBoxtsuoqiu.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxtsuoqiu.DropDownWidth = 70;
            comboBoxtsuoqiu.FillColor = Color.White;
            comboBoxtsuoqiu.FillColorGradient = true;
            comboBoxtsuoqiu.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxtsuoqiu.ItemHeight = 40;
            comboBoxtsuoqiu.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxtsuoqiu.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboBoxtsuoqiu.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxtsuoqiu.Location = new Point(175, 40);
            comboBoxtsuoqiu.Margin = new Padding(0);
            comboBoxtsuoqiu.MaxDropDownItems = 30;
            comboBoxtsuoqiu.MinimumSize = new Size(63, 0);
            comboBoxtsuoqiu.Name = "comboBoxtsuoqiu";
            comboBoxtsuoqiu.Padding = new Padding(0, 0, 30, 10);
            comboBoxtsuoqiu.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxtsuoqiu.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxtsuoqiu.ScrollBarStyleInherited = false;
            comboBoxtsuoqiu.Size = new Size(70, 31);
            comboBoxtsuoqiu.SymbolSize = 24;
            comboBoxtsuoqiu.TabIndex = 15;
            comboBoxtsuoqiu.Text = "A";
            comboBoxtsuoqiu.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxtsuoqiu.Watermark = "";
            comboBoxtsuoqiu.SelectedIndexChanged += comboBoxtsuoqiu_SelectedIndexChanged;
            // 
            // checkboxsuoqiu
            // 
            checkboxsuoqiu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsuoqiu.Location = new Point(250, 44);
            checkboxsuoqiu.Name = "checkboxsuoqiu";
            checkboxsuoqiu.Size = new Size(135, 23);
            checkboxsuoqiu.TabIndex = 12;
            checkboxsuoqiu.Text = "启用";
            checkboxsuoqiu.CheckedChanged += checkboxsuoqiu_CheckedChanged;
            // 
            // label12
            // 
            label12.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label12.Location = new Point(14, 44);
            label12.Name = "label12";
            label12.Size = new Size(161, 23);
            label12.TabIndex = 0;
            label12.Text = "锁球单独快捷键";
            // 
            // label13
            // 
            label13.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label13.ForeColor = Color.Green;
            label13.Location = new Point(17, 142);
            label13.Name = "label13";
            label13.Size = new Size(494, 21);
            label13.TabIndex = 59;
            label13.Text = "开启启用后即刻生效，关闭启用后需退出幷重进一次对局即可生效";
            label13.TextAlign = ContentAlignment.TopLeft;
            // 
            // fuzhugongneng
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(uiGroupBox1);
            Controls.Add(Grouptongbu);
            FormBorderStyle = FormBorderStyle.None;
            Name = "fuzhugongneng";
            Text = "fuzhugongneng";
            Load += fuzhugongneng_Load;
            Grouptongbu.ResumeLayout(false);
            uiGroupBox1.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Checkbox checkboxtb1;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboBoxtb1;
        public Sunny.UI.UIComboBox comboBoxmnj1;
        public AntdUI.Label label2;
        public Sunny.UI.UIComboBox comboBoxmnj3;
        public AntdUI.Label label5;
        public Sunny.UI.UIComboBox comboBoxtb3;
        public AntdUI.Checkbox checkboxtb3;
        public AntdUI.Label label6;
        public Sunny.UI.UIComboBox comboBoxmnj2;
        public AntdUI.Label label3;
        public Sunny.UI.UIComboBox comboBoxtb2;
        public AntdUI.Checkbox checkboxtb2;
        public AntdUI.Label label4;
        public Sunny.UI.UIGroupBox uiGroupBox1;
        public Sunny.UI.UIComboBox comboBoxtsuoqiu;
        public AntdUI.Checkbox checkboxsuoqiu;
        public AntdUI.Label label12;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxkongqiu;
        public Sunny.UI.UITextBox textboxkongqiu;
        public AntdUI.Label label8;
        public AntdUI.Label label19;
        public AntdUI.Label label9;
        public AntdUI.Checkbox checkboxygtm;
        public AntdUI.Label label10;
        public AntdUI.Label label11;
        public AntdUI.Label label13;
    }
}