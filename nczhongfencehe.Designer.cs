﻿namespace ANYE_Balls
{
    partial class nczhongfencehe
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewnczfch = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxnczfchtq = new AntdUI.Checkbox();
            textboxnczfchjcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxnczhongfencehe = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxnczhongfencehe = new AntdUI.Checkbox();
            buttonnczfchcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewnczfch).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewnczfch
            // 
            datagridviewnczfch.AllowUserToAddRows = false;
            datagridviewnczfch.AllowUserToDeleteRows = false;
            datagridviewnczfch.AllowUserToResizeColumns = false;
            datagridviewnczfch.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewnczfch.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewnczfch.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewnczfch.BorderStyle = BorderStyle.Fixed3D;
            datagridviewnczfch.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewnczfch.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewnczfch.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewnczfch.ColumnHeadersHeight = 40;
            datagridviewnczfch.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewnczfch.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewnczfch.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewnczfch.EnableHeadersVisualStyles = false;
            datagridviewnczfch.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewnczfch.GridColor = Color.AliceBlue;
            datagridviewnczfch.Location = new Point(309, 184);
            datagridviewnczfch.Name = "datagridviewnczfch";
            datagridviewnczfch.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewnczfch.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewnczfch.RowHeadersVisible = false;
            datagridviewnczfch.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewnczfch.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewnczfch.RowTemplate.Height = 25;
            datagridviewnczfch.ScrollBars = ScrollBars.None;
            datagridviewnczfch.SelectedIndex = -1;
            datagridviewnczfch.Size = new Size(294, 300);
            datagridviewnczfch.TabIndex = 74;
            datagridviewnczfch.CellValidating += datagridviewnczfch_CellValidating;
            datagridviewnczfch.CellValueChanged += datagridviewnczfch_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxnczfchtq
            // 
            checkboxnczfchtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxnczfchtq.Location = new Point(607, 186);
            checkboxnczfchtq.Name = "checkboxnczfchtq";
            checkboxnczfchtq.Size = new Size(131, 23);
            checkboxnczfchtq.TabIndex = 73;
            checkboxnczfchtq.Text = "自动吐球";
            checkboxnczfchtq.CheckedChanged += checkboxnczfchtq_CheckedChanged;
            // 
            // textboxnczfchjcfd
            // 
            textboxnczfchjcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxnczfchjcfd.Location = new Point(125, 231);
            textboxnczfchjcfd.Margin = new Padding(4, 5, 4, 5);
            textboxnczfchjcfd.MinimumSize = new Size(1, 16);
            textboxnczfchjcfd.Name = "textboxnczfchjcfd";
            textboxnczfchjcfd.Padding = new Padding(5);
            textboxnczfchjcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxnczfchjcfd.ShowText = false;
            textboxnczfchjcfd.Size = new Size(70, 30);
            textboxnczfchjcfd.TabIndex = 72;
            textboxnczfchjcfd.Text = "0";
            textboxnczfchjcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxnczfchjcfd.Watermark = "";
            textboxnczfchjcfd.TextChanged += textboxnczfchjcfd_TextChanged;
            textboxnczfchjcfd.KeyPress += textboxnczfchjcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 71;
            label1.Text = "交叉幅度";
            // 
            // comboboxnczhongfencehe
            // 
            comboboxnczhongfencehe.DataSource = null;
            comboboxnczhongfencehe.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxnczhongfencehe.DropDownWidth = 200;
            comboboxnczhongfencehe.FillColor = Color.White;
            comboboxnczhongfencehe.FillColorGradient = true;
            comboboxnczhongfencehe.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxnczhongfencehe.ItemHeight = 40;
            comboboxnczhongfencehe.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxnczhongfencehe.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxnczhongfencehe.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxnczhongfencehe.Location = new Point(125, 180);
            comboboxnczhongfencehe.Margin = new Padding(0);
            comboboxnczhongfencehe.MinimumSize = new Size(63, 0);
            comboboxnczhongfencehe.Name = "comboboxnczhongfencehe";
            comboboxnczhongfencehe.Padding = new Padding(0, 0, 30, 10);
            comboboxnczhongfencehe.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxnczhongfencehe.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxnczhongfencehe.ScrollBarStyleInherited = false;
            comboboxnczhongfencehe.Size = new Size(70, 31);
            comboboxnczhongfencehe.SymbolSize = 24;
            comboboxnczhongfencehe.TabIndex = 70;
            comboboxnczhongfencehe.Text = "A";
            comboboxnczhongfencehe.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxnczhongfencehe.Watermark = "";
            comboboxnczhongfencehe.SelectedIndexChanged += comboboxnczhongfencehe_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 67;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 69;
            label7.Text = "快捷键";
            // 
            // checkboxnczhongfencehe
            // 
            checkboxnczhongfencehe.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxnczhongfencehe.Location = new Point(208, 184);
            checkboxnczhongfencehe.Name = "checkboxnczhongfencehe";
            checkboxnczhongfencehe.Size = new Size(95, 23);
            checkboxnczhongfencehe.TabIndex = 68;
            checkboxnczhongfencehe.Text = "启用";
            checkboxnczhongfencehe.CheckedChanged += checkboxnczhongfencehe_CheckedChanged;
            // 
            // buttonnczfchcz
            // 
            buttonnczfchcz.BackHover = Color.Aquamarine;
            buttonnczfchcz.DefaultBack = Color.Azure;
            buttonnczfchcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonnczfchcz.Location = new Point(208, 231);
            buttonnczfchcz.Name = "buttonnczfchcz";
            buttonnczfchcz.Size = new Size(95, 64);
            buttonnczfchcz.TabIndex = 146;
            buttonnczfchcz.Text = "重置";
            buttonnczfchcz.Click += buttonnczfchcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标放在哪个位置就往哪个位置合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // nczhongfencehe
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonnczfchcz);
            Controls.Add(datagridviewnczfch);
            Controls.Add(checkboxnczfchtq);
            Controls.Add(textboxnczfchjcfd);
            Controls.Add(label1);
            Controls.Add(comboboxnczhongfencehe);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxnczhongfencehe);
            FormBorderStyle = FormBorderStyle.None;
            Name = "nczhongfencehe";
            Text = "nczhongfencehe";
            Load += nczhongfencehe_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewnczfch).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewnczfch;
        public AntdUI.Checkbox checkboxnczfchtq;
        public Sunny.UI.UITextBox textboxnczfchjcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxnczhongfencehe;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxnczhongfencehe;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonnczfchcz;
        public AntdUI.Label label19;
    }
}