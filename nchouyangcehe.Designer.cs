﻿namespace ANYE_Balls
{
    partial class nchouyangcehe
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            textboxnchychjd = new Sunny.UI.UITextBox();
            datagridviewnchych = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxnchychtq = new AntdUI.Checkbox();
            textboxnchychjcfd = new Sunny.UI.UITextBox();
            comboboxnchouyangcehe = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxnchouyangcehe = new AntdUI.Checkbox();
            label2 = new AntdUI.Label();
            label1 = new AntdUI.Label();
            buttonnchychcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewnchych).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // textboxnchychjd
            // 
            textboxnchychjd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxnchychjd.Location = new Point(125, 265);
            textboxnchychjd.Margin = new Padding(4, 5, 4, 5);
            textboxnchychjd.MinimumSize = new Size(1, 16);
            textboxnchychjd.Name = "textboxnchychjd";
            textboxnchychjd.Padding = new Padding(5);
            textboxnchychjd.RectColor = Color.FromArgb(255, 255, 192);
            textboxnchychjd.ShowText = false;
            textboxnchychjd.Size = new Size(70, 30);
            textboxnchychjd.TabIndex = 84;
            textboxnchychjd.Text = "0";
            textboxnchychjd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxnchychjd.Watermark = "";
            textboxnchychjd.TextChanged += textboxnchychjd_TextChanged;
            textboxnchychjd.KeyPress += textboxnchychjd_KeyPress;
            // 
            // datagridviewnchych
            // 
            datagridviewnchych.AllowUserToAddRows = false;
            datagridviewnchych.AllowUserToDeleteRows = false;
            datagridviewnchych.AllowUserToResizeColumns = false;
            datagridviewnchych.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewnchych.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewnchych.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewnchych.BorderStyle = BorderStyle.Fixed3D;
            datagridviewnchych.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewnchych.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewnchych.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewnchych.ColumnHeadersHeight = 40;
            datagridviewnchych.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewnchych.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewnchych.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewnchych.EnableHeadersVisualStyles = false;
            datagridviewnchych.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewnchych.GridColor = Color.AliceBlue;
            datagridviewnchych.Location = new Point(309, 184);
            datagridviewnchych.Name = "datagridviewnchych";
            datagridviewnchych.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewnchych.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewnchych.RowHeadersVisible = false;
            datagridviewnchych.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewnchych.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewnchych.RowTemplate.Height = 25;
            datagridviewnchych.ScrollBars = ScrollBars.None;
            datagridviewnchych.SelectedIndex = -1;
            datagridviewnchych.Size = new Size(294, 300);
            datagridviewnchych.TabIndex = 83;
            datagridviewnchych.CellValidating += datagridviewnchych_CellValidating;
            datagridviewnchych.CellValueChanged += datagridviewnchych_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxnchychtq
            // 
            checkboxnchychtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxnchychtq.Location = new Point(607, 186);
            checkboxnchychtq.Name = "checkboxnchychtq";
            checkboxnchychtq.Size = new Size(131, 23);
            checkboxnchychtq.TabIndex = 82;
            checkboxnchychtq.Text = "自动吐球";
            checkboxnchychtq.CheckedChanged += checkboxnchychtq_CheckedChanged;
            // 
            // textboxnchychjcfd
            // 
            textboxnchychjcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxnchychjcfd.Location = new Point(125, 231);
            textboxnchychjcfd.Margin = new Padding(4, 5, 4, 5);
            textboxnchychjcfd.MinimumSize = new Size(1, 16);
            textboxnchychjcfd.Name = "textboxnchychjcfd";
            textboxnchychjcfd.Padding = new Padding(5);
            textboxnchychjcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxnchychjcfd.ShowText = false;
            textboxnchychjcfd.Size = new Size(70, 30);
            textboxnchychjcfd.TabIndex = 81;
            textboxnchychjcfd.Text = "0";
            textboxnchychjcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxnchychjcfd.Watermark = "";
            textboxnchychjcfd.TextChanged += textboxnchychjcfd_TextChanged;
            textboxnchychjcfd.KeyPress += textboxnchychjcfd_KeyPress;
            // 
            // comboboxnchouyangcehe
            // 
            comboboxnchouyangcehe.DataSource = null;
            comboboxnchouyangcehe.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxnchouyangcehe.DropDownWidth = 70;
            comboboxnchouyangcehe.FillColor = Color.White;
            comboboxnchouyangcehe.FillColorGradient = true;
            comboboxnchouyangcehe.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxnchouyangcehe.ItemHeight = 40;
            comboboxnchouyangcehe.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxnchouyangcehe.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxnchouyangcehe.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxnchouyangcehe.Location = new Point(125, 180);
            comboboxnchouyangcehe.Margin = new Padding(0);
            comboboxnchouyangcehe.MaxDropDownItems = 30;
            comboboxnchouyangcehe.MinimumSize = new Size(63, 0);
            comboboxnchouyangcehe.Name = "comboboxnchouyangcehe";
            comboboxnchouyangcehe.Padding = new Padding(0, 0, 30, 10);
            comboboxnchouyangcehe.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxnchouyangcehe.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxnchouyangcehe.ScrollBarStyleInherited = false;
            comboboxnchouyangcehe.Size = new Size(70, 31);
            comboboxnchouyangcehe.SymbolSize = 24;
            comboboxnchouyangcehe.TabIndex = 80;
            comboboxnchouyangcehe.Text = "A";
            comboboxnchouyangcehe.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxnchouyangcehe.Watermark = "";
            comboboxnchouyangcehe.SelectedIndexChanged += comboboxnchouyangcehe_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 77;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 79;
            label7.Text = "快捷键";
            // 
            // checkboxnchouyangcehe
            // 
            checkboxnchouyangcehe.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxnchouyangcehe.Location = new Point(208, 184);
            checkboxnchouyangcehe.Name = "checkboxnchouyangcehe";
            checkboxnchouyangcehe.Size = new Size(95, 23);
            checkboxnchouyangcehe.TabIndex = 78;
            checkboxnchouyangcehe.Text = "启用";
            checkboxnchouyangcehe.CheckedChanged += checkboxnchouyangcehe_CheckedChanged;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(28, 269);
            label2.Name = "label2";
            label2.Size = new Size(100, 23);
            label2.TabIndex = 86;
            label2.Text = "后仰角度";
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 85;
            label1.Text = "交叉幅度";
            // 
            // buttonnchychcz
            // 
            buttonnchychcz.BackHover = Color.Aquamarine;
            buttonnchychcz.DefaultBack = Color.Azure;
            buttonnchychcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonnchychcz.Location = new Point(208, 231);
            buttonnchychcz.Name = "buttonnchychcz";
            buttonnchychcz.Size = new Size(95, 64);
            buttonnchychcz.TabIndex = 146;
            buttonnchychcz.Text = "重置";
            buttonnchychcz.Click += buttonnchychcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 55;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标放在哪个位置就往哪个位置合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // nchouyangcehe
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonnchychcz);
            Controls.Add(label2);
            Controls.Add(label1);
            Controls.Add(textboxnchychjd);
            Controls.Add(datagridviewnchych);
            Controls.Add(checkboxnchychtq);
            Controls.Add(textboxnchychjcfd);
            Controls.Add(comboboxnchouyangcehe);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxnchouyangcehe);
            FormBorderStyle = FormBorderStyle.None;
            Name = "nchouyangcehe";
            Text = "nchouyangcehe";
            Load += nchouyangcehe_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewnchych).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewnchych;
        public Sunny.UI.UITextBox textboxnchychjd;
        public AntdUI.Checkbox checkboxnchychtq;
        public Sunny.UI.UITextBox textboxnchychjcfd;
        public Sunny.UI.UIComboBox comboboxnchouyangcehe;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxnchouyangcehe;
        public AntdUI.Label label2;
        public AntdUI.Label label1;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonnchychcz;
        public AntdUI.Label label19;
    }
}