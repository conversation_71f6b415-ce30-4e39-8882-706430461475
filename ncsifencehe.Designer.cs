﻿namespace ANYE_Balls
{
    partial class ncsifencehe
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            datagridviewncsfch = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            checkboxncsfchtq = new AntdUI.Checkbox();
            textboxncsfchjcfd = new Sunny.UI.UITextBox();
            label1 = new AntdUI.Label();
            comboboxncsifencehe = new Sunny.UI.UIComboBox();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            label7 = new AntdUI.Label();
            checkboxncsifencehe = new AntdUI.Checkbox();
            buttonncsfchcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            ((System.ComponentModel.ISupportInitialize)datagridviewncsfch).BeginInit();
            Grouptongbu.SuspendLayout();
            SuspendLayout();
            // 
            // datagridviewncsfch
            // 
            datagridviewncsfch.AllowUserToAddRows = false;
            datagridviewncsfch.AllowUserToDeleteRows = false;
            datagridviewncsfch.AllowUserToResizeColumns = false;
            datagridviewncsfch.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewncsfch.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewncsfch.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewncsfch.BorderStyle = BorderStyle.Fixed3D;
            datagridviewncsfch.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewncsfch.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewncsfch.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewncsfch.ColumnHeadersHeight = 40;
            datagridviewncsfch.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewncsfch.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewncsfch.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewncsfch.EnableHeadersVisualStyles = false;
            datagridviewncsfch.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewncsfch.GridColor = Color.AliceBlue;
            datagridviewncsfch.Location = new Point(309, 184);
            datagridviewncsfch.Name = "datagridviewncsfch";
            datagridviewncsfch.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewncsfch.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewncsfch.RowHeadersVisible = false;
            datagridviewncsfch.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewncsfch.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewncsfch.RowTemplate.Height = 25;
            datagridviewncsfch.ScrollBars = ScrollBars.None;
            datagridviewncsfch.SelectedIndex = -1;
            datagridviewncsfch.Size = new Size(294, 300);
            datagridviewncsfch.TabIndex = 66;
            datagridviewncsfch.CellValidating += datagridviewncsfch_CellValidating;
            datagridviewncsfch.CellValueChanged += datagridviewncsfch_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // checkboxncsfchtq
            // 
            checkboxncsfchtq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsfchtq.Location = new Point(607, 186);
            checkboxncsfchtq.Name = "checkboxncsfchtq";
            checkboxncsfchtq.Size = new Size(131, 23);
            checkboxncsfchtq.TabIndex = 65;
            checkboxncsfchtq.Text = "自动吐球";
            checkboxncsfchtq.CheckedChanged += checkboxncsfchtq_CheckedChanged;
            // 
            // textboxncsfchjcfd
            // 
            textboxncsfchjcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxncsfchjcfd.Location = new Point(125, 231);
            textboxncsfchjcfd.Margin = new Padding(4, 5, 4, 5);
            textboxncsfchjcfd.MinimumSize = new Size(1, 16);
            textboxncsfchjcfd.Name = "textboxncsfchjcfd";
            textboxncsfchjcfd.Padding = new Padding(5);
            textboxncsfchjcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxncsfchjcfd.ShowText = false;
            textboxncsfchjcfd.Size = new Size(70, 30);
            textboxncsfchjcfd.TabIndex = 64;
            textboxncsfchjcfd.Text = "0";
            textboxncsfchjcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxncsfchjcfd.Watermark = "";
            textboxncsfchjcfd.TextChanged += textboxncsfchjcfd_TextChanged;
            textboxncsfchjcfd.KeyPress += textboxncsfchjcfd_KeyPress;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 63;
            label1.Text = "交叉幅度";
            // 
            // comboboxncsifencehe
            // 
            comboboxncsifencehe.DataSource = null;
            comboboxncsifencehe.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxncsifencehe.DropDownWidth = 70;
            comboboxncsifencehe.FillColor = Color.White;
            comboboxncsifencehe.FillColorGradient = true;
            comboboxncsifencehe.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxncsifencehe.ItemHeight = 40;
            comboboxncsifencehe.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxncsifencehe.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxncsifencehe.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxncsifencehe.Location = new Point(125, 180);
            comboboxncsifencehe.Margin = new Padding(0);
            comboboxncsifencehe.MaxDropDownItems = 30;
            comboboxncsifencehe.MinimumSize = new Size(63, 0);
            comboboxncsifencehe.Name = "comboboxncsifencehe";
            comboboxncsifencehe.Padding = new Padding(0, 0, 30, 10);
            comboboxncsifencehe.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxncsifencehe.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxncsifencehe.ScrollBarStyleInherited = false;
            comboboxncsifencehe.Size = new Size(70, 31);
            comboboxncsifencehe.SymbolSize = 24;
            comboboxncsifencehe.TabIndex = 62;
            comboboxncsifencehe.Text = "A";
            comboboxncsifencehe.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxncsifencehe.Watermark = "";
            comboboxncsifencehe.SelectedIndexChanged += comboboxncsifencehe_SelectedIndexChanged;
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 59;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 61;
            label7.Text = "快捷键";
            // 
            // checkboxncsifencehe
            // 
            checkboxncsifencehe.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxncsifencehe.Location = new Point(208, 184);
            checkboxncsifencehe.Name = "checkboxncsifencehe";
            checkboxncsifencehe.Size = new Size(95, 23);
            checkboxncsifencehe.TabIndex = 60;
            checkboxncsifencehe.Text = "启用";
            checkboxncsifencehe.CheckedChanged += checkboxncsifencehe_CheckedChanged;
            // 
            // buttonncsfchcz
            // 
            buttonncsfchcz.BackHover = Color.Aquamarine;
            buttonncsfchcz.DefaultBack = Color.Azure;
            buttonncsfchcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonncsfchcz.Location = new Point(208, 231);
            buttonncsfchcz.Name = "buttonncsfchcz";
            buttonncsfchcz.Size = new Size(95, 64);
            buttonncsfchcz.TabIndex = 146;
            buttonncsfchcz.Text = "重置";
            buttonncsfchcz.Click += buttonncsfchcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 55;
            label19.Text = "遥杆方向决定第一次分身方向，鼠标放在哪个位置就往哪个位置合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // ncsifencehe
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonncsfchcz);
            Controls.Add(datagridviewncsfch);
            Controls.Add(checkboxncsfchtq);
            Controls.Add(textboxncsfchjcfd);
            Controls.Add(label1);
            Controls.Add(comboboxncsifencehe);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxncsifencehe);
            FormBorderStyle = FormBorderStyle.None;
            Name = "ncsifencehe";
            Text = "ncsifencehe";
            Load += ncsifencehe_Load;
            ((System.ComponentModel.ISupportInitialize)datagridviewncsfch).EndInit();
            Grouptongbu.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewncsfch;
        public AntdUI.Checkbox checkboxncsfchtq;
        public Sunny.UI.UITextBox textboxncsfchjcfd;
        public AntdUI.Label label1;
        public Sunny.UI.UIComboBox comboboxncsifencehe;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxncsifencehe;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonncsfchcz;
        public AntdUI.Label label19;
    }
}