﻿namespace ANYE_Balls
{
    partial class pifugongneng
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            uiGroupBox2 = new Sunny.UI.UIGroupBox();
            buttonqupihf = new AntdUI.Button();
            buttonqupi = new AntdUI.Button();
            uiGroupBox1 = new Sunny.UI.UIGroupBox();
            comboBoxtmc = new Sunny.UI.UIComboBox();
            label2 = new AntdUI.Label();
            buttontmchf = new AntdUI.Button();
            buttontmc = new AntdUI.Button();
            uiGroupBox3 = new Sunny.UI.UIGroupBox();
            checkboxlwblk = new AntdUI.Checkbox();
            buttonqlwhf = new AntdUI.Button();
            buttonqlw = new AntdUI.Button();
            buttonqhwhf = new AntdUI.Button();
            buttonqhw = new AntdUI.Button();
            label19 = new AntdUI.Label();
            label1 = new AntdUI.Label();
            label3 = new AntdUI.Label();
            uiGroupBox2.SuspendLayout();
            uiGroupBox1.SuspendLayout();
            uiGroupBox3.SuspendLayout();
            SuspendLayout();
            // 
            // uiGroupBox2
            // 
            uiGroupBox2.Controls.Add(label19);
            uiGroupBox2.Controls.Add(buttonqupihf);
            uiGroupBox2.Controls.Add(buttonqupi);
            uiGroupBox2.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox2.Location = new Point(14, 5);
            uiGroupBox2.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox2.MinimumSize = new Size(1, 1);
            uiGroupBox2.Name = "uiGroupBox2";
            uiGroupBox2.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox2.Radius = 20;
            uiGroupBox2.RectColor = SystemColors.ActiveCaption;
            uiGroupBox2.Size = new Size(753, 91);
            uiGroupBox2.TabIndex = 45;
            uiGroupBox2.Text = "去皮";
            uiGroupBox2.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // buttonqupihf
            // 
            buttonqupihf.BackHover = Color.Aquamarine;
            buttonqupihf.DefaultBack = Color.Azure;
            buttonqupihf.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonqupihf.Location = new Point(132, 35);
            buttonqupihf.Name = "buttonqupihf";
            buttonqupihf.Size = new Size(111, 40);
            buttonqupihf.TabIndex = 19;
            buttonqupihf.Text = "恢复";
            buttonqupihf.Click += buttonqupihf_Click;
            // 
            // buttonqupi
            // 
            buttonqupi.BackHover = Color.Aquamarine;
            buttonqupi.DefaultBack = Color.Azure;
            buttonqupi.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonqupi.Location = new Point(15, 35);
            buttonqupi.Name = "buttonqupi";
            buttonqupi.Size = new Size(111, 40);
            buttonqupi.TabIndex = 18;
            buttonqupi.Text = "去皮";
            buttonqupi.Click += buttonqupi_Click;
            // 
            // uiGroupBox1
            // 
            uiGroupBox1.Controls.Add(label1);
            uiGroupBox1.Controls.Add(comboBoxtmc);
            uiGroupBox1.Controls.Add(label2);
            uiGroupBox1.Controls.Add(buttontmchf);
            uiGroupBox1.Controls.Add(buttontmc);
            uiGroupBox1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox1.Location = new Point(14, 97);
            uiGroupBox1.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox1.MinimumSize = new Size(1, 1);
            uiGroupBox1.Name = "uiGroupBox1";
            uiGroupBox1.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox1.Radius = 20;
            uiGroupBox1.RectColor = SystemColors.ActiveCaption;
            uiGroupBox1.Size = new Size(753, 148);
            uiGroupBox1.TabIndex = 46;
            uiGroupBox1.Text = "透明刺";
            uiGroupBox1.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // comboBoxtmc
            // 
            comboBoxtmc.DataSource = null;
            comboBoxtmc.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboBoxtmc.DropDownWidth = 70;
            comboBoxtmc.FillColor = Color.White;
            comboBoxtmc.FillColorGradient = true;
            comboBoxtmc.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboBoxtmc.ItemHeight = 40;
            comboBoxtmc.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboBoxtmc.Items.AddRange(new object[] { "经典透明刺", "蓝光无", "无", "多环蓝圈" });
            comboBoxtmc.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboBoxtmc.Location = new Point(74, 39);
            comboBoxtmc.Margin = new Padding(0);
            comboBoxtmc.MaxDropDownItems = 30;
            comboBoxtmc.MinimumSize = new Size(63, 0);
            comboBoxtmc.Name = "comboBoxtmc";
            comboBoxtmc.Padding = new Padding(0, 0, 30, 10);
            comboBoxtmc.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboBoxtmc.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboBoxtmc.ScrollBarStyleInherited = false;
            comboBoxtmc.Size = new Size(154, 31);
            comboBoxtmc.SymbolSize = 24;
            comboBoxtmc.TabIndex = 46;
            comboBoxtmc.Text = "经典透明刺";
            comboBoxtmc.TextAlignment = ContentAlignment.MiddleLeft;
            comboBoxtmc.Watermark = "";
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(17, 44);
            label2.Name = "label2";
            label2.Size = new Size(51, 23);
            label2.TabIndex = 45;
            label2.Text = "皮肤";
            // 
            // buttontmchf
            // 
            buttontmchf.BackHover = Color.Aquamarine;
            buttontmchf.DefaultBack = Color.Azure;
            buttontmchf.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttontmchf.Location = new Point(358, 35);
            buttontmchf.Name = "buttontmchf";
            buttontmchf.Size = new Size(111, 40);
            buttontmchf.TabIndex = 19;
            buttontmchf.Text = "还原";
            buttontmchf.Click += buttontmchf_Click;
            // 
            // buttontmc
            // 
            buttontmc.BackHover = Color.Aquamarine;
            buttontmc.DefaultBack = Color.Azure;
            buttontmc.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttontmc.Location = new Point(241, 35);
            buttontmc.Name = "buttontmc";
            buttontmc.Size = new Size(111, 40);
            buttontmc.TabIndex = 18;
            buttontmc.Text = "开启";
            buttontmc.Click += buttontmc_Click;
            // 
            // uiGroupBox3
            // 
            uiGroupBox3.Controls.Add(label3);
            uiGroupBox3.Controls.Add(checkboxlwblk);
            uiGroupBox3.Controls.Add(buttonqlwhf);
            uiGroupBox3.Controls.Add(buttonqlw);
            uiGroupBox3.Controls.Add(buttonqhwhf);
            uiGroupBox3.Controls.Add(buttonqhw);
            uiGroupBox3.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            uiGroupBox3.Location = new Point(14, 246);
            uiGroupBox3.Margin = new Padding(4, 5, 4, 5);
            uiGroupBox3.MinimumSize = new Size(1, 1);
            uiGroupBox3.Name = "uiGroupBox3";
            uiGroupBox3.Padding = new Padding(0, 32, 0, 0);
            uiGroupBox3.Radius = 20;
            uiGroupBox3.RectColor = SystemColors.ActiveCaption;
            uiGroupBox3.Size = new Size(753, 187);
            uiGroupBox3.TabIndex = 47;
            uiGroupBox3.Text = "去雾";
            uiGroupBox3.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // checkboxlwblk
            // 
            checkboxlwblk.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxlwblk.Location = new Point(488, 44);
            checkboxlwblk.Name = "checkboxlwblk";
            checkboxlwblk.Size = new Size(153, 23);
            checkboxlwblk.TabIndex = 46;
            checkboxlwblk.Text = "蓝雾不留坑";
            checkboxlwblk.Visible = false;
            // 
            // buttonqlwhf
            // 
            buttonqlwhf.BackHover = Color.Aquamarine;
            buttonqlwhf.DefaultBack = Color.Azure;
            buttonqlwhf.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonqlwhf.Location = new Point(369, 35);
            buttonqlwhf.Name = "buttonqlwhf";
            buttonqlwhf.Size = new Size(111, 40);
            buttonqlwhf.TabIndex = 21;
            buttonqlwhf.Text = "还原";
            buttonqlwhf.Click += buttonqlwhf_Click;
            // 
            // buttonqlw
            // 
            buttonqlw.BackHover = Color.Aquamarine;
            buttonqlw.DefaultBack = Color.Azure;
            buttonqlw.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonqlw.Location = new Point(252, 35);
            buttonqlw.Name = "buttonqlw";
            buttonqlw.Size = new Size(111, 40);
            buttonqlw.TabIndex = 20;
            buttonqlw.Text = "去蓝雾";
            buttonqlw.Click += buttonqlw_Click;
            // 
            // buttonqhwhf
            // 
            buttonqhwhf.BackHover = Color.Aquamarine;
            buttonqhwhf.DefaultBack = Color.Azure;
            buttonqhwhf.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonqhwhf.Location = new Point(134, 35);
            buttonqhwhf.Name = "buttonqhwhf";
            buttonqhwhf.Size = new Size(111, 40);
            buttonqhwhf.TabIndex = 19;
            buttonqhwhf.Text = "还原";
            buttonqhwhf.Click += buttonqhwhf_Click;
            // 
            // buttonqhw
            // 
            buttonqhw.BackHover = Color.Aquamarine;
            buttonqhw.DefaultBack = Color.Azure;
            buttonqhw.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonqhw.Location = new Point(17, 35);
            buttonqhw.Name = "buttonqhw";
            buttonqhw.Size = new Size(111, 40);
            buttonqhw.TabIndex = 18;
            buttonqhw.Text = "去红雾";
            buttonqhw.Click += buttonqhw_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(246, 44);
            label19.Name = "label19";
            label19.Size = new Size(498, 28);
            label19.TabIndex = 56;
            label19.Text = "开启模拟器ADB调试和Root权限，修改后大退游戏生效";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label1.ForeColor = Color.Green;
            label1.Location = new Point(17, 81);
            label1.Name = "label1";
            label1.Size = new Size(498, 28);
            label1.TabIndex = 57;
            label1.Text = "开启模拟器ADB调试和Root权限，修改后大退游戏生效";
            label1.TextAlign = ContentAlignment.TopLeft;
            // 
            // label3
            // 
            label3.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label3.ForeColor = Color.Green;
            label3.Location = new Point(17, 81);
            label3.Name = "label3";
            label3.Size = new Size(498, 28);
            label3.TabIndex = 58;
            label3.Text = "开启模拟器ADB调试和Root权限，修改后大退游戏生效";
            label3.TextAlign = ContentAlignment.TopLeft;
            // 
            // pifugongneng
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(uiGroupBox3);
            Controls.Add(uiGroupBox1);
            Controls.Add(uiGroupBox2);
            FormBorderStyle = FormBorderStyle.None;
            Name = "pifugongneng";
            Text = "pifugongneng";
            Load += pifugongneng_Load;
            uiGroupBox2.ResumeLayout(false);
            uiGroupBox1.ResumeLayout(false);
            uiGroupBox3.ResumeLayout(false);
            ResumeLayout(false);
        }

        #endregion

        private Sunny.UI.UIGroupBox uiGroupBox2;
        private AntdUI.Button buttonqupihf;
        private AntdUI.Button buttonqupi;
        private Sunny.UI.UIGroupBox uiGroupBox1;
        private AntdUI.Button buttontmchf;
        private AntdUI.Button buttontmc;
        private Sunny.UI.UIComboBox comboBoxtmc;
        private AntdUI.Label label2;
        private Sunny.UI.UIGroupBox uiGroupBox3;
        private AntdUI.Button buttonqhwhf;
        private AntdUI.Button buttonqhw;
        private AntdUI.Button buttonqlwhf;
        private AntdUI.Button buttonqlw;
        private AntdUI.Checkbox checkboxlwblk;
        public AntdUI.Label label19;
        public AntdUI.Label label1;
        public AntdUI.Label label3;
    }
}