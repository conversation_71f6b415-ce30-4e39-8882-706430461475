﻿namespace ANYE_Balls
{
    partial class sanjiao1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            Grouptongbu = new Sunny.UI.UIGroupBox();
            comboboxsanjiao1 = new Sunny.UI.UIComboBox();
            label7 = new AntdUI.Label();
            checkboxsanjiao1 = new AntdUI.Checkbox();
            label1 = new AntdUI.Label();
            textboxsj1jcfd = new Sunny.UI.UITextBox();
            textboxsj1jd = new Sunny.UI.UITextBox();
            label2 = new AntdUI.Label();
            checkboxsj1tq = new AntdUI.Checkbox();
            checkboxsj1jt = new AntdUI.Checkbox();
            datagridviewsj1 = new Sunny.UI.UIDataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            buttonsjcz = new AntdUI.Button();
            label19 = new AntdUI.Label();
            Grouptongbu.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)datagridviewsj1).BeginInit();
            SuspendLayout();
            // 
            // Grouptongbu
            // 
            Grouptongbu.Controls.Add(label19);
            Grouptongbu.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Bold, GraphicsUnit.Point);
            Grouptongbu.Location = new Point(14, 5);
            Grouptongbu.Margin = new Padding(4, 5, 4, 5);
            Grouptongbu.MinimumSize = new Size(1, 1);
            Grouptongbu.Name = "Grouptongbu";
            Grouptongbu.Padding = new Padding(0, 32, 0, 0);
            Grouptongbu.Radius = 20;
            Grouptongbu.RectColor = SystemColors.ActiveCaption;
            Grouptongbu.Size = new Size(753, 164);
            Grouptongbu.TabIndex = 2;
            Grouptongbu.Text = "注意事项";
            Grouptongbu.TextAlignment = ContentAlignment.MiddleLeft;
            // 
            // comboboxsanjiao1
            // 
            comboboxsanjiao1.DataSource = null;
            comboboxsanjiao1.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            comboboxsanjiao1.DropDownWidth = 70;
            comboboxsanjiao1.FillColor = Color.White;
            comboboxsanjiao1.FillColorGradient = true;
            comboboxsanjiao1.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Bold, GraphicsUnit.Point);
            comboboxsanjiao1.ItemHeight = 40;
            comboboxsanjiao1.ItemHoverColor = Color.FromArgb(155, 200, 255);
            comboboxsanjiao1.Items.AddRange(new object[] { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "空格", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" });
            comboboxsanjiao1.ItemSelectForeColor = Color.FromArgb(235, 243, 255);
            comboboxsanjiao1.Location = new Point(125, 180);
            comboboxsanjiao1.Margin = new Padding(0);
            comboboxsanjiao1.MaxDropDownItems = 30;
            comboboxsanjiao1.MinimumSize = new Size(63, 0);
            comboboxsanjiao1.Name = "comboboxsanjiao1";
            comboboxsanjiao1.Padding = new Padding(0, 0, 30, 10);
            comboboxsanjiao1.ScrollBarBackColor = Color.FromArgb(245, 251, 241);
            comboboxsanjiao1.ScrollBarColor = Color.FromArgb(110, 190, 40);
            comboboxsanjiao1.ScrollBarStyleInherited = false;
            comboboxsanjiao1.Size = new Size(70, 31);
            comboboxsanjiao1.SymbolSize = 24;
            comboboxsanjiao1.TabIndex = 29;
            comboboxsanjiao1.Text = "A";
            comboboxsanjiao1.TextAlignment = ContentAlignment.MiddleLeft;
            comboboxsanjiao1.Watermark = "";
            comboboxsanjiao1.SelectedIndexChanged += comboboxsanjiao1_SelectedIndexChanged;
            // 
            // label7
            // 
            label7.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label7.Location = new Point(48, 184);
            label7.Name = "label7";
            label7.Size = new Size(89, 23);
            label7.TabIndex = 28;
            label7.Text = "快捷键";
            // 
            // checkboxsanjiao1
            // 
            checkboxsanjiao1.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsanjiao1.Location = new Point(208, 184);
            checkboxsanjiao1.Name = "checkboxsanjiao1";
            checkboxsanjiao1.Size = new Size(95, 23);
            checkboxsanjiao1.TabIndex = 27;
            checkboxsanjiao1.Text = "启用";
            checkboxsanjiao1.CheckedChanged += checkboxsanjiao1_CheckedChanged;
            // 
            // label1
            // 
            label1.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label1.Location = new Point(28, 235);
            label1.Name = "label1";
            label1.Size = new Size(100, 23);
            label1.TabIndex = 30;
            label1.Text = "交叉幅度";
            // 
            // textboxsj1jcfd
            // 
            textboxsj1jcfd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxsj1jcfd.Location = new Point(125, 231);
            textboxsj1jcfd.Margin = new Padding(4, 5, 4, 5);
            textboxsj1jcfd.MinimumSize = new Size(1, 16);
            textboxsj1jcfd.Name = "textboxsj1jcfd";
            textboxsj1jcfd.Padding = new Padding(5);
            textboxsj1jcfd.RectColor = Color.FromArgb(255, 255, 192);
            textboxsj1jcfd.ShowText = false;
            textboxsj1jcfd.Size = new Size(70, 30);
            textboxsj1jcfd.TabIndex = 31;
            textboxsj1jcfd.Text = "0";
            textboxsj1jcfd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxsj1jcfd.Watermark = "";
            textboxsj1jcfd.TextChanged += textboxsj1jcfd_TextChanged;
            textboxsj1jcfd.KeyPress += textboxsj1jcfd_KeyPress;
            // 
            // textboxsj1jd
            // 
            textboxsj1jd.Font = new Font("Microsoft YaHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            textboxsj1jd.Location = new Point(125, 265);
            textboxsj1jd.Margin = new Padding(4, 5, 4, 5);
            textboxsj1jd.MinimumSize = new Size(1, 16);
            textboxsj1jd.Name = "textboxsj1jd";
            textboxsj1jd.Padding = new Padding(5);
            textboxsj1jd.RectColor = Color.FromArgb(255, 255, 192);
            textboxsj1jd.ShowText = false;
            textboxsj1jd.Size = new Size(70, 30);
            textboxsj1jd.TabIndex = 33;
            textboxsj1jd.Text = "0";
            textboxsj1jd.TextAlignment = ContentAlignment.MiddleCenter;
            textboxsj1jd.Watermark = "";
            textboxsj1jd.TextChanged += textboxsj1jd_TextChanged;
            textboxsj1jd.KeyPress += textboxsj1jd_KeyPress;
            // 
            // label2
            // 
            label2.Font = new Font("Microsoft JhengHei UI", 15.75F, FontStyle.Regular, GraphicsUnit.Point);
            label2.Location = new Point(71, 269);
            label2.Name = "label2";
            label2.Size = new Size(57, 23);
            label2.TabIndex = 32;
            label2.Text = "角度";
            // 
            // checkboxsj1tq
            // 
            checkboxsj1tq.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsj1tq.Location = new Point(607, 186);
            checkboxsj1tq.Name = "checkboxsj1tq";
            checkboxsj1tq.Size = new Size(131, 23);
            checkboxsj1tq.TabIndex = 34;
            checkboxsj1tq.Text = "自动吐球";
            checkboxsj1tq.CheckedChanged += checkboxsj1tq_CheckedChanged;
            // 
            // checkboxsj1jt
            // 
            checkboxsj1jt.Font = new Font("Microsoft JhengHei UI", 15F, FontStyle.Regular, GraphicsUnit.Point);
            checkboxsj1jt.Location = new Point(607, 215);
            checkboxsj1jt.Name = "checkboxsj1jt";
            checkboxsj1jt.Size = new Size(167, 23);
            checkboxsj1jt.TabIndex = 35;
            checkboxsj1jt.Text = "箭头方向为准";
            checkboxsj1jt.CheckedChanged += checkboxsj1jt_CheckedChanged;
            // 
            // datagridviewsj1
            // 
            datagridviewsj1.AllowUserToAddRows = false;
            datagridviewsj1.AllowUserToDeleteRows = false;
            datagridviewsj1.AllowUserToResizeColumns = false;
            datagridviewsj1.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.BackColor = Color.FromArgb(243, 249, 255);
            datagridviewsj1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            datagridviewsj1.BackgroundColor = Color.FromArgb(243, 249, 255);
            datagridviewsj1.BorderStyle = BorderStyle.Fixed3D;
            datagridviewsj1.CellBorderStyle = DataGridViewCellBorderStyle.Sunken;
            datagridviewsj1.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = SystemColors.ActiveCaption;
            dataGridViewCellStyle2.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle2.ForeColor = Color.White;
            dataGridViewCellStyle2.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = DataGridViewTriState.True;
            datagridviewsj1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            datagridviewsj1.ColumnHeadersHeight = 40;
            datagridviewsj1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            datagridviewsj1.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2 });
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = Color.White;
            dataGridViewCellStyle5.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle5.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle5.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            datagridviewsj1.DefaultCellStyle = dataGridViewCellStyle5;
            datagridviewsj1.EnableHeadersVisualStyles = false;
            datagridviewsj1.Font = new Font("宋体", 12F, FontStyle.Regular, GraphicsUnit.Point);
            datagridviewsj1.GridColor = Color.AliceBlue;
            datagridviewsj1.Location = new Point(309, 184);
            datagridviewsj1.Name = "datagridviewsj1";
            datagridviewsj1.RectColor = Color.FromArgb(128, 255, 255);
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle6.BackColor = Color.FromArgb(243, 249, 255);
            dataGridViewCellStyle6.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle6.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.SelectionBackColor = Color.FromArgb(80, 160, 255);
            dataGridViewCellStyle6.SelectionForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            datagridviewsj1.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            datagridviewsj1.RowHeadersVisible = false;
            datagridviewsj1.RowHeadersWidth = 146;
            dataGridViewCellStyle7.BackColor = Color.White;
            dataGridViewCellStyle7.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            dataGridViewCellStyle7.ForeColor = Color.FromArgb(48, 48, 48);
            dataGridViewCellStyle7.SelectionBackColor = Color.FromArgb(220, 236, 255);
            dataGridViewCellStyle7.SelectionForeColor = Color.FromArgb(48, 48, 48);
            datagridviewsj1.RowsDefaultCellStyle = dataGridViewCellStyle7;
            datagridviewsj1.RowTemplate.Height = 25;
            datagridviewsj1.ScrollBars = ScrollBars.None;
            datagridviewsj1.SelectedIndex = -1;
            datagridviewsj1.Size = new Size(294, 300);
            datagridviewsj1.TabIndex = 36;
            datagridviewsj1.CellValidating += datagridviewsj1_CellValidating;
            datagridviewsj1.CellValueChanged += datagridviewsj1_CellValueChanged;
            // 
            // Column1
            // 
            dataGridViewCellStyle3.BackColor = Color.AliceBlue;
            dataGridViewCellStyle3.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column1.DefaultCellStyle = dataGridViewCellStyle3;
            Column1.HeaderText = "操作";
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column1.Width = 146;
            // 
            // Column2
            // 
            dataGridViewCellStyle4.Font = new Font("Microsoft JhengHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point);
            Column2.DefaultCellStyle = dataGridViewCellStyle4;
            Column2.HeaderText = "延迟";
            Column2.Name = "Column2";
            Column2.SortMode = DataGridViewColumnSortMode.NotSortable;
            Column2.Width = 146;
            // 
            // buttonsjcz
            // 
            buttonsjcz.BackHover = Color.Aquamarine;
            buttonsjcz.DefaultBack = Color.Azure;
            buttonsjcz.Font = new Font("微軟正黑體", 15F, FontStyle.Regular, GraphicsUnit.Point);
            buttonsjcz.Location = new Point(208, 231);
            buttonsjcz.Name = "buttonsjcz";
            buttonsjcz.Size = new Size(95, 64);
            buttonsjcz.TabIndex = 37;
            buttonsjcz.Text = "重置";
            buttonsjcz.Click += buttonsjcz_Click;
            // 
            // label19
            // 
            label19.Font = new Font("Microsoft YaHei UI", 11.25F, FontStyle.Regular, GraphicsUnit.Point);
            label19.ForeColor = Color.Green;
            label19.Location = new Point(18, 38);
            label19.Name = "label19";
            label19.Size = new Size(718, 111);
            label19.TabIndex = 56;
            label19.Text = "鼠标方向：鼠标放在哪里往哪里合\r\n箭头方向：遥杆指向哪里往哪里合";
            label19.TextAlign = ContentAlignment.TopLeft;
            // 
            // sanjiao1
            // 
            AutoScaleMode = AutoScaleMode.None;
            AutoSizeMode = AutoSizeMode.GrowAndShrink;
            BackColor = Color.AliceBlue;
            ClientSize = new Size(779, 815);
            Controls.Add(buttonsjcz);
            Controls.Add(datagridviewsj1);
            Controls.Add(checkboxsj1jt);
            Controls.Add(checkboxsj1tq);
            Controls.Add(textboxsj1jd);
            Controls.Add(label2);
            Controls.Add(textboxsj1jcfd);
            Controls.Add(label1);
            Controls.Add(comboboxsanjiao1);
            Controls.Add(Grouptongbu);
            Controls.Add(label7);
            Controls.Add(checkboxsanjiao1);
            FormBorderStyle = FormBorderStyle.None;
            Name = "sanjiao1";
            Text = "sanjiao1";
            Load += sanjiao1_Load;
            Grouptongbu.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)datagridviewsj1).EndInit();
            ResumeLayout(false);
        }

        #endregion
        public Sunny.UI.UIDataGridView datagridviewsj1;
        public Sunny.UI.UIGroupBox Grouptongbu;
        public Sunny.UI.UIComboBox comboboxsanjiao1;
        public AntdUI.Label label7;
        public AntdUI.Checkbox checkboxsanjiao1;
        public AntdUI.Label label1;
        public Sunny.UI.UITextBox textboxsj1jcfd;
        public Sunny.UI.UITextBox textboxsj1jd;
        public AntdUI.Label label2;
        public AntdUI.Checkbox checkboxsj1tq;
        public AntdUI.Checkbox checkboxsj1jt;
        public DataGridViewTextBoxColumn Column1;
        public DataGridViewTextBoxColumn Column2;
        public AntdUI.Button buttonsjcz;
        public AntdUI.Label label19;
    }
}